#!/usr/bin/env node

/**
 * Turbo Performance Monitoring Script
 * Measures build times, hot reload performance, and bundle sizes
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TurboPerformanceMonitor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      turboEnabled: process.env.TURBOPACK_ENABLED === 'true',
      buildTimes: {},
      bundleSizes: {},
      hotReloadTimes: []
    };
  }

  // Measure build time
  async measureBuildTime(command = 'npm run build') {
    console.log('🚀 Measuring build time...');
    const startTime = Date.now();
    
    try {
      execSync(command, { stdio: 'inherit' });
      const buildTime = Date.now() - startTime;
      this.results.buildTimes[command] = buildTime;
      console.log(`✅ Build completed in ${buildTime}ms`);
      return buildTime;
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      throw error;
    }
  }

  // Measure bundle sizes
  measureBundleSize() {
    console.log('📦 Measuring bundle sizes...');
    const nextDir = path.join(process.cwd(), '.next');
    
    if (!fs.existsSync(nextDir)) {
      console.warn('⚠️ .next directory not found. Run build first.');
      return;
    }

    const staticDir = path.join(nextDir, 'static');
    if (fs.existsSync(staticDir)) {
      const chunks = this.getDirectorySize(staticDir);
      this.results.bundleSizes.static = chunks;
      console.log(`📊 Static bundle size: ${this.formatBytes(chunks)}`);
    }

    const serverDir = path.join(nextDir, 'server');
    if (fs.existsSync(serverDir)) {
      const serverSize = this.getDirectorySize(serverDir);
      this.results.bundleSizes.server = serverSize;
      console.log(`🖥️ Server bundle size: ${this.formatBytes(serverSize)}`);
    }
  }

  // Get directory size recursively
  getDirectorySize(dirPath) {
    let totalSize = 0;
    
    const files = fs.readdirSync(dirPath);
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        totalSize += this.getDirectorySize(filePath);
      } else {
        totalSize += stats.size;
      }
    }
    
    return totalSize;
  }

  // Format bytes to human readable
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Compare with baseline
  compareWithBaseline(baselineFile = 'performance-baseline.json') {
    const baselinePath = path.join(process.cwd(), baselineFile);
    
    if (!fs.existsSync(baselinePath)) {
      console.log('📝 No baseline found. Creating baseline...');
      this.saveBaseline(baselineFile);
      return;
    }

    const baseline = JSON.parse(fs.readFileSync(baselinePath, 'utf8'));
    console.log('\n📊 Performance Comparison:');
    
    // Compare build times
    Object.entries(this.results.buildTimes).forEach(([command, time]) => {
      const baselineTime = baseline.buildTimes?.[command];
      if (baselineTime) {
        const improvement = ((baselineTime - time) / baselineTime) * 100;
        const symbol = improvement > 0 ? '🚀' : '🐌';
        console.log(`${symbol} ${command}: ${improvement > 0 ? '+' : ''}${improvement.toFixed(2)}% (${time}ms vs ${baselineTime}ms)`);
      }
    });

    // Compare bundle sizes
    Object.entries(this.results.bundleSizes).forEach(([type, size]) => {
      const baselineSize = baseline.bundleSizes?.[type];
      if (baselineSize) {
        const change = ((size - baselineSize) / baselineSize) * 100;
        const symbol = change < 0 ? '📉' : '📈';
        console.log(`${symbol} ${type} bundle: ${change > 0 ? '+' : ''}${change.toFixed(2)}% (${this.formatBytes(size)} vs ${this.formatBytes(baselineSize)})`);
      }
    });
  }

  // Save current results as baseline
  saveBaseline(filename = 'performance-baseline.json') {
    const baselinePath = path.join(process.cwd(), filename);
    fs.writeFileSync(baselinePath, JSON.stringify(this.results, null, 2));
    console.log(`💾 Baseline saved to ${filename}`);
  }

  // Save results
  saveResults(filename = `performance-${Date.now()}.json`) {
    const resultsPath = path.join(process.cwd(), 'performance-reports', filename);
    
    // Create directory if it doesn't exist
    const dir = path.dirname(resultsPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(resultsPath, JSON.stringify(this.results, null, 2));
    console.log(`📄 Results saved to ${resultsPath}`);
  }

  // Run full performance test
  async runFullTest() {
    console.log('🎯 Starting Turbo Performance Test...\n');
    
    try {
      // Clean build
      console.log('🧹 Cleaning previous build...');
      execSync('npm run clean', { stdio: 'inherit' });
      
      // Measure build time
      await this.measureBuildTime();
      
      // Measure bundle sizes
      this.measureBundleSize();
      
      // Compare with baseline
      this.compareWithBaseline();
      
      // Save results
      this.saveResults();
      
      console.log('\n✅ Performance test completed!');
      
    } catch (error) {
      console.error('❌ Performance test failed:', error.message);
      process.exit(1);
    }
  }
}

// CLI interface
const args = process.argv.slice(2);
const command = args[0];

const monitor = new TurboPerformanceMonitor();

switch (command) {
  case 'build':
    monitor.measureBuildTime().then(() => {
      monitor.measureBundleSize();
      monitor.saveResults();
    });
    break;
  
  case 'baseline':
    monitor.runFullTest().then(() => {
      monitor.saveBaseline();
    });
    break;
  
  case 'compare':
    monitor.runFullTest();
    break;
  
  default:
    console.log(`
🚀 Turbo Performance Monitor

Usage:
  node scripts/turbo-performance.js [command]

Commands:
  build     - Measure build time and bundle size
  baseline  - Run full test and save as baseline
  compare   - Run full test and compare with baseline

Examples:
  node scripts/turbo-performance.js build
  node scripts/turbo-performance.js baseline
  node scripts/turbo-performance.js compare
    `);
}

{"name": "frontend2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:legacy": "next dev", "dev:debug": "next dev --turbo --inspect", "dev:perf": "NEXT_TELEMETRY_DISABLED=1 next dev --turbo", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:debug": "NEXT_DEBUG=1 next build", "start": "next start", "start:prod": "NODE_ENV=production next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "clean": "rm -rf .next out", "clean:all": "rm -rf .next out node_modules package-lock.json && npm install", "perf:build": "time npm run build", "perf:dev": "time next dev --turbo --port 3000", "perf:test": "node scripts/turbo-performance.js compare", "perf:baseline": "node scripts/turbo-performance.js baseline", "perf:monitor": "node scripts/turbo-performance.js build"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.80.7", "axios": "^1.9.0", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "lucide-react": "^0.514.0", "next": "15.3.3", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.5.4", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.4", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "next-compose-plugins": "^2.2.1", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}
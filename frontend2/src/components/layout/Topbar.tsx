'use client';

import React from 'react';
import {
  <PERSON>S<PERSON>ch,
  <PERSON>Bell,
  <PERSON><PERSON>ser,
  FiSettings,
  FiLogOut,
  FiSun,
  FiMoon,
  FiMenu,
  FiChevronDown,
  FiMapPin,
} from 'react-icons/fi';
import { cn } from '@/lib/utils';
import { api, endpoints } from '@/lib/api';
import { Badge, Button, Input, Text } from '@/components/ui';
import { useClinicIdentity } from '../../contexts/ClinicBrandingContext';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme, useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import ClinicSwitchDialog from '../dialogs/ClinicSwitchDialog';
import { useRouter } from 'next/navigation';

interface TopbarProps {
  onMobileMenuOpen: () => void;
  sidebarWidth: string;
}



interface Notification {
  id: string;
  title: string;
  time: string;
  unread: boolean;
  type?: string;
  createdAt?: string;
}

export default function Topbar({ onMobileMenuOpen, sidebarWidth }: TopbarProps) {
  const { actualTheme, toggleTheme } = useTheme();
  const themeClasses = useThemeClasses();
  const { isDark, colors } = useClinicTheme();
  const { name: clinicName } = useClinicIdentity();
  const { user, profile, currentClinic, availableClinics, logout, loadProfile, switchClinic } = useAuth();
  const router = useRouter();

  const [searchQuery, setSearchQuery] = React.useState('');
  const [isClinicSwitchOpen, setIsClinicSwitchOpen] = React.useState(false);
  const [isSwitchingClinic, setIsSwitchingClinic] = React.useState(false);
  const [notifications, setNotifications] = React.useState<Notification[]>([]);
  const [loadingNotifications, setLoadingNotifications] = React.useState(false);

  const unreadNotifications = notifications.filter(n => n.unread).length;

  // Fetch notifications from API
  React.useEffect(() => {
    const fetchNotifications = async () => {
      try {
        setLoadingNotifications(true);

        // Fetch user notifications
        const response = await api.get('/api/notifications?limit=5&unreadFirst=true');

        if (response.data.success) {
          // Transform API data to match UI interface
          const transformedNotifications: Notification[] = response.data.data.map((notif: any) => ({
            id: notif.id || notif._id,
            title: notif.title || notif.message || 'New notification',
            time: notif.timeAgo || 'Just now',
            unread: !notif.isRead,
            type: notif.type || 'general',
            createdAt: notif.createdAt
          }));

          setNotifications(transformedNotifications);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
        // Set empty array on error
        setNotifications([]);
      } finally {
        setLoadingNotifications(false);
      }
    };

    if (user) {
      fetchNotifications();
    }
  }, [user]);

  // Ensure profile is loaded
  React.useEffect(() => {
    if (user && !profile) {
      loadProfile();
    }
  }, [user, profile, loadProfile]);

  const handleClinicSwitchClick = () => {
    setIsClinicSwitchOpen(true);
  };

  const handleLogout = () => {
    logout();
  };

  const handleClinicSwitch = async (clinicId: string) => {
    if (isSwitchingClinic || clinicId === currentClinic?.clinic?.id) {
      return;
    }

    try {
      setIsSwitchingClinic(true);
      await switchClinic(clinicId);

      // Optional: Show success message
      const selectedClinic = availableClinics.find(c => c.clinic.id === clinicId);
      console.log('Switched to clinic:', selectedClinic?.clinic?.name);

      // Refresh the page to ensure all components load with new clinic context
      window.location.reload();
    } catch (error: any) {
      console.error('Error switching clinic:', error);
      // You could show an error toast here
    } finally {
      setIsSwitchingClinic(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log('Searching for:', searchQuery);
  };

  const handleThemeToggle = () => {
    toggleTheme();
  };

  // Helper functions for user display
  const getUserDisplayName = () => {
    if (profile?.user?.fullName) return profile.user.fullName;
    if (user?.fullName) return user.fullName;
    if (user?.firstName && user?.lastName) return `${user.firstName} ${user.lastName}`;
    if (profile?.user?.firstName && profile?.user?.lastName) return `${profile.user.firstName} ${profile.user.lastName}`;
    return 'User';
  };

  const getUserEmail = () => {
    return profile?.user?.email || user?.email || '';
  };

  const getUserAvatar = () => {
    return profile?.user?.avatar || user?.avatar || '';
  };

  const getUserTitle = () => {
    // Check primary user role first (new system)
    if (user?.roleDetails?.displayName) {
      return user.roleDetails.displayName;
    }

    // Try to get role from current clinic
    if (currentClinic?.role) {
      return currentClinic.role.displayName || currentClinic.role.name;
    }

    // Fallback to first active role from profile
    if (profile?.user?.clinicRoles && profile.user.clinicRoles.length > 0) {
      const activeRole = profile.user.clinicRoles.find(role => role.isActive);
      if (activeRole?.role) {
        return activeRole.role.displayName || activeRole.role.name;
      }
    }

    // Check if user has any role-specific data
    if (profile?.roleSpecificData) {
      if (profile.roleSpecificData.staff) return 'Staff Member';
      if (profile.roleSpecificData.client) return 'Client';
      if (profile.roleSpecificData.freelancer) return 'Freelancer';
    }

    return '';
  };

  return (
    <div
      className={cn(
        'fixed top-0 right-0 h-20 z-[999]',
        'backdrop-blur-xl border-b transition-all duration-300 ease-smooth shadow-medium',
        themeClasses.topbar
      )}
      style={{
        left: sidebarWidth,
        background: isDark
          ? `linear-gradient(to right, ${colors.primary}10, ${colors.secondary}05), rgba(31, 41, 55, 0.95)`
          : `linear-gradient(to right, ${colors.primary}10, ${colors.secondary}05), rgba(255, 255, 255, 0.95)`,
      }}
    >
      <div className="flex items-center justify-between h-full px-8">
        {/* Left side - Mobile menu + Breadcrumbs */}
        <div className="flex items-center gap-4">
          <button
            aria-label="Open mobile menu"
            onClick={onMobileMenuOpen}
            className="lg:hidden p-2 rounded-xl hover:bg-gray-100 transition-colors"
          >
            <FiMenu className="w-5 h-5 text-gray-600" />
          </button>

          {/* Breadcrumbs */}
          <nav className="hidden md:flex items-center gap-2 text-sm">
            <a href="/dashboard" className="text-gray-600 hover:text-primary-600 transition-colors">
              Dashboard
            </a>
            {/* Add dynamic breadcrumbs based on current route */}
          </nav>
        </div>

        {/* Center - Search */}
        <div className="hidden md:block flex-1 max-w-lg mx-8">
          <form onSubmit={handleSearch}>
            <div className="relative">
              <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-blue-400" />
              <input
                type="text"
                placeholder="Search patients, appointments, clients..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={cn(
                  'w-full pl-12 pr-4 py-3 rounded-2xl text-md',
                  'bg-white/80 backdrop-blur-sm border border-white/30',
                  'placeholder-gray-500 text-gray-900',
                  'focus:bg-white/95 focus:border-blue-300 focus:outline-none',
                  'focus:ring-4 focus:ring-primary-500/10 focus:-translate-y-0.5',
                  'hover:border-blue-200',
                  'transition-all duration-300 ease-smooth'
                )}
              />
            </div>
          </form>
        </div>

        {/* Right side - Actions */}
        <div className="flex items-center gap-4">
          {/* Enhanced Clinic Switcher */}
          {(currentClinic || availableClinics.length > 0) && (
            <div className="relative group hidden lg:block">
              <button
                className={cn(
                  'flex items-center gap-2 px-4 py-3 rounded-2xl',
                  'bg-white/80 backdrop-blur-sm border border-white/30',
                  'hover:bg-white/95 hover:border-blue-200 hover:-translate-y-0.5',
                  'hover:shadow-medium transition-all duration-300 ease-smooth'
                )}
              >
                <FiMapPin className="w-4 h-4 text-blue-500" />
                <div className="flex flex-col items-start">
                  <span className="font-semibold text-gray-700 max-w-[150px] truncate text-sm">
                    {currentClinic?.clinic?.name || clinicName || 'Select Clinic'}
                  </span>
                  {currentClinic?.role && (
                    <span className="text-xs text-gray-500 max-w-[150px] truncate">
                      {currentClinic.role.displayName || currentClinic.role.name}
                    </span>
                  )}
                </div>
                <FiChevronDown className="w-4 h-4 text-gray-500" />
              </button>

              {/* Enhanced Dropdown Menu */}
              <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-large border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="px-4 py-3 border-b border-gray-100">
                  <Text variant="bodySmall" className="font-semibold text-gray-600">
                    Available Clinics ({availableClinics.length})
                  </Text>
                  {currentClinic && (
                    <Text variant="bodySmall" className="text-gray-500 mt-1">
                      Currently: {currentClinic.clinic?.name}
                    </Text>
                  )}
                </div>

                <div className="max-h-64 overflow-y-auto">
                  {availableClinics.map((clinic) => {
                    const isCurrentClinic = currentClinic?.clinic?.id === clinic.clinic.id;
                    const isOwner = clinic.clinic.stats?.isOwner || clinic.role?.name === 'clinic_owner';

                    return (
                      <button
                        key={clinic.clinic.id}
                        onClick={() => !isCurrentClinic && handleClinicSwitch(clinic.clinic.id)}
                        className={cn(
                          'w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors text-left',
                          isCurrentClinic && 'bg-blue-50 border-l-4 border-blue-500'
                        )}
                        disabled={isCurrentClinic}
                      >
                        <div className={cn(
                          'w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-semibold',
                          isCurrentClinic ? 'bg-blue-500' : 'bg-gray-400'
                        )}>
                          {clinic.clinic.name?.charAt(0).toUpperCase() || 'C'}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900 truncate">
                              {clinic.clinic.name}
                            </span>
                            {isOwner && (
                              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                                Owner
                              </span>
                            )}
                            {isCurrentClinic && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                                Current
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-sm text-gray-500">
                              {clinic.role?.displayName || clinic.role?.name || 'Staff'}
                            </span>
                            {clinic.clinic.stats && (
                              <span className="text-xs text-gray-400">
                                • {clinic.clinic.stats.staffCount} staff
                              </span>
                            )}
                          </div>
                        </div>
                        {!isCurrentClinic && (
                          <FiChevronDown className="w-4 h-4 text-gray-400 transform -rotate-90" />
                        )}
                      </button>
                    );
                  })}
                </div>

                <div className="px-4 py-3 border-t border-gray-100">
                  <button
                    onClick={handleClinicSwitchClick}
                    className="w-full flex items-center gap-3 px-3 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    <FiMapPin className="w-4 h-4" />
                    <span className="text-sm font-medium">Manage Clinics</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Theme Toggle */}
          <button
            aria-label="Toggle color mode"
            onClick={handleThemeToggle}
            className={cn(
              'p-3 rounded-2xl text-blue-500 backdrop-blur-sm border transition-all duration-300 ease-smooth',
              'hover:-translate-y-0.5 hover:rotate-180 hover:shadow-medium',
              actualTheme === 'dark'
                ? 'bg-gray-700/80 border-gray-600/50 hover:bg-gray-600/95 hover:border-blue-400'
                : 'bg-white/80 border-white/30 hover:bg-white/95 hover:border-blue-200'
            )}
          >
            {actualTheme === 'dark' ? (
              <FiSun className="w-5 h-5" />
            ) : (
              <FiMoon className="w-5 h-5" />
            )}
          </button>

          {/* Notifications */}
          <div className="relative group">
            <button
              aria-label="Notifications"
              className={cn(
                'relative p-3 rounded-2xl text-blue-500',
                'bg-white/80 backdrop-blur-sm border border-white/30',
                'hover:bg-white/95 hover:border-blue-200 hover:-translate-y-0.5',
                'hover:shadow-medium transition-all duration-300 ease-smooth'
              )}
            >
              <FiBell className="w-5 h-5" />
              {unreadNotifications > 0 && (
                <Badge
                  variant="error"
                  size="sm"
                  className={cn(
                    'absolute -top-1 -right-1 min-w-[20px] h-5',
                    'flex items-center justify-center',
                    'bg-gradient-to-br from-red-400 to-pink-400 text-white',
                    'border-2 border-white shadow-medium'
                  )}
                >
                  {unreadNotifications}
                </Badge>
              )}
            </button>

            {/* Notifications Dropdown */}
            <div className="absolute right-0 top-full mt-2 w-80 bg-white rounded-xl shadow-large border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div className="px-3 py-2">
                <Text variant="bodySmall" className="font-semibold text-gray-600">
                  Notifications
                </Text>
              </div>
              <hr className="border-gray-200" />
              {loadingNotifications ? (
                // Loading skeleton
                [1, 2, 3].map((index) => (
                  <div key={index} className="px-3 py-3 animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))
              ) : notifications.length > 0 ? (
                notifications.map((notification) => (
                <div key={notification.id} className="px-3 py-3 hover:bg-gray-50 transition-colors">
                  <div>
                    <p className={cn(
                      'text-sm',
                      notification.unread ? 'font-semibold text-gray-900' : 'font-normal text-gray-700'
                    )}>
                      {notification.title}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {notification.time}
                    </p>
                  </div>
                </div>
              ))
              ) : (
                <div className="px-3 py-6 text-center">
                  <p className="text-sm text-gray-500">No notifications</p>
                </div>
              )}
              <hr className="border-gray-200" />
              <div className="px-3 py-2">
                <button className="text-sm text-primary-500 hover:text-primary-600 transition-colors">
                  View all notifications
                </button>
              </div>
            </div>
          </div>

          {/* User Menu */}
          <div className="relative group">
            <div className="flex items-center gap-3 cursor-pointer p-2 rounded-xl hover:bg-white/10 hover:-translate-y-0.5 transition-all duration-200">
              <div className="hidden md:block text-right">
                <p className="text-sm font-semibold text-gray-700 truncate">
                  {getUserDisplayName()}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {getUserTitle()}
                </p>
              </div>
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm border-2 border-white shadow-md">
                {getUserAvatar() ? (
                  <img
                    src={getUserAvatar()}
                    alt={getUserDisplayName()}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  getUserDisplayName().charAt(0).toUpperCase()
                )}
              </div>
            </div>

            {/* User Menu Dropdown */}
            <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-xl shadow-large border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div className="px-4 py-3">
                <p className="text-sm font-semibold text-gray-800">
                  {getUserDisplayName()}
                </p>
                <p className="text-xs text-gray-500 mt-0.5">
                  {getUserEmail()}
                </p>
                {getUserTitle() && (
                  <Badge
                    variant="primary"
                    size="sm"
                    className="mt-2"
                  >
                    {getUserTitle()}
                  </Badge>
                )}
                {currentClinic?.clinic && (
                  <p className="text-xs text-gray-400 mt-1">
                    @ {currentClinic.clinic.name}
                  </p>
                )}
              </div>
              <hr className="border-gray-200" />
              <button
                onClick={() => router.push('/profile')}
                className="w-full flex items-center gap-3 px-4 py-2 mx-2 rounded-md hover:bg-gray-50 transition-colors"
              >
                <FiUser className="w-4 h-4" />
                <span>Profile</span>
              </button>
              <button className="w-full flex items-center gap-3 px-4 py-2 mx-2 rounded-md hover:bg-gray-50 transition-colors">
                <FiSettings className="w-4 h-4" />
                <span>Settings</span>
              </button>
              <hr className="border-gray-200" />
              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-3 px-4 py-2 mx-2 rounded-md text-red-500 hover:bg-red-50 transition-colors"
              >
                <FiLogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Clinic Switch Dialog */}
      <ClinicSwitchDialog
        isOpen={isClinicSwitchOpen}
        onClose={() => setIsClinicSwitchOpen(false)}
      />
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  Building,
  Shield,
  Settings,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Eye,
  Edit3,
  Plus
} from 'lucide-react';

import DashboardLayout from '../../components/layout/DashboardLayout';
import { useAuth } from '../../contexts/AuthContext';
import { useThemeClasses, useClinicTheme } from '../../contexts/ThemeContext';
import { cn } from '@/lib/utils';
import { api, endpoints } from '@/lib/api';
import Link from 'next/link';

interface AdminStats {
  totalClinics: number;
  activeClinics: number;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  revenueGrowth: number;
  pendingApprovals: number;
  systemHealth: 'Good' | 'Warning' | 'Critical';
  totalClients: number;
  activeClients: number;
  totalPatients: number;
  activePatients: number;
  totalAppointments: number;
  todayAppointments: number;
  totalMedicalRecords: number;
  totalInvoices: number;
  paidInvoices: number;
  totalServices: number;
  totalServiceCategories: number;
  totalRoles: number;
  totalPermissions: number;
  totalSpecies: number;
  totalBreeds: number;
  recentActivity: RecentActivity[];
  clinicGrowth: number;
  userGrowth: number;
  appointmentGrowth: number;
}

interface RecentActivity {
  type: string;
  message: string;
  time: string;
  timestamp: string;
}

interface PendingApproval {
  id: number;
  type: 'species' | 'breed' | 'service_category' | 'service';
  name: string;
  requestedBy: string;
  clinic?: string;
  createdAt: string;
  adminAccepted: boolean;
}

export default function AdminPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const themeClasses = useThemeClasses();
  const { isDark, colors } = useClinicTheme();
  
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [loading, setLoading] = useState(true);

  // Check if user is admin
  const isAdmin = user?.role === 'super_admin' || user?.role === 'clinic_admin';

  // Handle approval actions
  const handleApproval = async (approval: PendingApproval, action: 'approve' | 'reject') => {
    try {
      const endpoint = action === 'approve'
        ? endpoints.admin[`approve${approval.type.charAt(0).toUpperCase() + approval.type.slice(1)}` as keyof typeof endpoints.admin]
        : endpoints.admin[`reject${approval.type.charAt(0).toUpperCase() + approval.type.slice(1)}` as keyof typeof endpoints.admin];

      if (typeof endpoint === 'function') {
        await api.put(endpoint(approval.id.toString()));

        // Remove the approval from the list
        setPendingApprovals(prev => prev.filter(item => item.id !== approval.id));

        // Update stats
        setStats(prev => prev ? { ...prev, pendingApprovals: prev.pendingApprovals - 1 } : null);
      }
    } catch (error) {
      console.error(`Failed to ${action} approval:`, error);
    }
  };

  // Fetch real admin data from API
  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        setLoading(true);

        // Fetch admin dashboard stats
        const statsResponse = await api.get('/api/admin-stats/dashboard');
        if (statsResponse.data) {
          const statsData = statsResponse.data;

          // Transform the data to match our interface
          const transformedStats: AdminStats = {
            totalClinics: statsData.totalClinics || 0,
            activeClinics: statsData.activeClinics || 0,
            totalUsers: statsData.totalUsers || 0,
            activeUsers: statsData.activeUsers || 0,
            totalRevenue: statsData.totalRevenue || 0,
            revenueGrowth: statsData.revenueGrowth || 0,
            pendingApprovals: 0, // Will be calculated from pending approvals
            systemHealth: 'Good', // Default to good, could be enhanced with health check
            totalClients: statsData.totalClients || 0,
            activeClients: statsData.activeClients || 0,
            totalPatients: statsData.totalPatients || 0,
            activePatients: statsData.activePatients || 0,
            totalAppointments: statsData.totalAppointments || 0,
            todayAppointments: statsData.todayAppointments || 0,
            totalMedicalRecords: statsData.totalMedicalRecords || 0,
            totalInvoices: statsData.totalInvoices || 0,
            paidInvoices: statsData.paidInvoices || 0,
            totalServices: statsData.totalServices || 0,
            totalServiceCategories: statsData.totalServiceCategories || 0,
            totalRoles: statsData.totalRoles || 0,
            totalPermissions: statsData.totalPermissions || 0,
            totalSpecies: statsData.totalSpecies || 0,
            totalBreeds: statsData.totalBreeds || 0,
            recentActivity: statsData.recentActivity || [],
            clinicGrowth: statsData.clinicGrowth || 0,
            userGrowth: statsData.userGrowth || 0,
            appointmentGrowth: statsData.appointmentGrowth || 0
          };

          setStats(transformedStats);
          setRecentActivity(statsData.recentActivity || []);
        }

        // Fetch pending approvals
        const approvalsResponse = await api.get(endpoints.admin.pendingApprovals);
        if (approvalsResponse.data.success) {
          const allApprovals: PendingApproval[] = [];

          // Combine all types of pending approvals
          if (approvalsResponse.data.data.species) {
            approvalsResponse.data.data.species.data.forEach((item: any) => {
              allApprovals.push({
                id: item.id,
                type: 'species',
                name: item.name,
                requestedBy: item.createdBy ? `${item.createdBy.firstName} ${item.createdBy.lastName}` : 'Unknown',
                clinic: item.clinic?.name || 'Global',
                createdAt: item.createdAt,
                adminAccepted: item.adminAccepted
              });
            });
          }

          if (approvalsResponse.data.data.breeds) {
            approvalsResponse.data.data.breeds.data.forEach((item: any) => {
              allApprovals.push({
                id: item.id,
                type: 'breed',
                name: item.name,
                requestedBy: item.createdBy ? `${item.createdBy.firstName} ${item.createdBy.lastName}` : 'Unknown',
                clinic: item.clinic?.name || 'Global',
                createdAt: item.createdAt,
                adminAccepted: item.adminAccepted
              });
            });
          }

          if (approvalsResponse.data.data.serviceCategories) {
            approvalsResponse.data.data.serviceCategories.data.forEach((item: any) => {
              allApprovals.push({
                id: item.id,
                type: 'service_category',
                name: item.name,
                requestedBy: item.createdBy ? `${item.createdBy.firstName} ${item.createdBy.lastName}` : 'Unknown',
                clinic: item.clinic?.name || 'Global',
                createdAt: item.createdAt,
                adminAccepted: item.adminAccepted
              });
            });
          }

          if (approvalsResponse.data.data.services) {
            approvalsResponse.data.data.services.data.forEach((item: any) => {
              allApprovals.push({
                id: item.id,
                type: 'service',
                name: item.name,
                requestedBy: item.createdBy ? `${item.createdBy.firstName} ${item.createdBy.lastName}` : 'Unknown',
                clinic: item.clinic?.name || 'Global',
                createdAt: item.createdAt,
                adminAccepted: item.adminAccepted
              });
            });
          }

          setPendingApprovals(allApprovals);

          // Update pending approvals count in stats
          if (stats) {
            setStats(prev => prev ? { ...prev, pendingApprovals: allApprovals.length } : null);
          }
        }

      } catch (error) {
        console.error('Failed to fetch admin data:', error);
        // Set empty data on error
        setStats(null);
        setRecentActivity([]);
        setPendingApprovals([]);
      } finally {
        setLoading(false);
      }
    };

    if (isAdmin) {
      fetchAdminData();
    }
  }, [isAdmin]);

  // Show loading state
  if (authLoading || loading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading admin dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show access denied if not admin
  if (!isAdmin) {
    return (
      <DashboardLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-6">
          <div className="flex flex-col items-center space-y-4">
            <Shield className="h-16 w-16 text-gray-400" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Access Denied</h2>
            <p className="text-gray-600 dark:text-gray-400 text-center">
              You don't have permission to access the admin dashboard. This page is only accessible to administrators.
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div
        className={cn("min-h-screen p-6", themeClasses.bgSecondary)}
        style={{
          background: isDark
            ? `linear-gradient(135deg, ${colors.primary}05, ${colors.secondary}03), ${themeClasses.bgSecondary.replace('bg-', '')}`
            : `linear-gradient(135deg, ${colors.primary}08, ${colors.secondary}05), ${themeClasses.bgSecondary.replace('bg-', '')}`
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h1 className={cn("text-2xl font-bold", themeClasses.text)}>
                  Admin Dashboard
                </h1>
                <p className={themeClasses.textSecondary}>
                  System overview and management
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Link href="/admin/settings">
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <Settings className="h-4 w-4 mr-2" />
                    System Settings
                  </button>
                </Link>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Clinics */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Clinics</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats?.totalClinics}
                    </p>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <Building className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-sm text-green-600 dark:text-green-400">
                    {stats?.activeClinics} active
                  </span>
                </div>
              </div>

              {/* Total Users */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Users</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {stats?.totalUsers}
                    </p>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                    <Users className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-sm text-green-600 dark:text-green-400">
                    {stats?.activeUsers} active
                  </span>
                </div>
              </div>

              {/* Total Revenue */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Platform Revenue</p>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      ${stats?.totalRevenue.toLocaleString()}
                    </p>
                  </div>
                  <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                    <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600 dark:text-green-400">
                    +{stats?.revenueGrowth}% this month
                  </span>
                </div>
              </div>

              {/* System Health */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">System Health</p>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {stats?.systemHealth}
                    </p>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                    <Activity className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    All systems operational
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/admin/users">
                <div className={cn("p-6 rounded-lg cursor-pointer hover:shadow-lg transition-shadow", themeClasses.card)}>
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
                      <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">User Management</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Manage all users</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/clinics">
                <div className={cn("p-6 rounded-lg cursor-pointer hover:shadow-lg transition-shadow", themeClasses.card)}>
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-green-100 dark:bg-green-900 rounded-full">
                      <Building className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Clinic Management</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Manage all clinics</p>
                    </div>
                  </div>
                </div>
              </Link>

              <Link href="/admin/species-breeds">
                <div className={cn("p-6 rounded-lg cursor-pointer hover:shadow-lg transition-shadow", themeClasses.card)}>
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full">
                      <Shield className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Species & Breeds</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Manage approvals</p>
                    </div>
                  </div>
                </div>
              </Link>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pending Approvals */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Pending Approvals ({stats?.pendingApprovals})
                  </h3>
                  <Link href="/admin/species-breeds">
                    <button className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                      View all
                    </button>
                  </Link>
                </div>
                
                <div className="space-y-4">
                  {pendingApprovals.map((approval) => (
                    <div key={approval.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                          <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {approval.name}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {approval.type} • {approval.clinic}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500">
                            Requested by {approval.requestedBy}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleApproval(approval, 'approve')}
                          className="p-2 text-green-600 hover:bg-green-50 dark:hover:bg-green-900 rounded"
                          title="Approve"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleApproval(approval, 'reject')}
                          className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 rounded"
                          title="Reject"
                        >
                          <XCircle className="h-4 w-4" />
                        </button>
                        <Link href={`/admin/${approval.type === 'service_category' ? 'services' : approval.type === 'service' ? 'services' : 'species-breeds'}`}>
                          <button className="p-2 text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded" title="View Details">
                            <Eye className="h-4 w-4" />
                          </button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Recent Activity */}
              <div className={cn("p-6 rounded-lg", themeClasses.card)}>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Activity</h3>
                </div>
                
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-4">
                      <div className={`p-2 rounded-full ${
                        activity.status === 'success' 
                          ? 'bg-green-100 dark:bg-green-900'
                          : activity.status === 'warning'
                          ? 'bg-yellow-100 dark:bg-yellow-900'
                          : activity.status === 'error'
                          ? 'bg-red-100 dark:bg-red-900'
                          : 'bg-blue-100 dark:bg-blue-900'
                      }`}>
                        {activity.status === 'success' ? (
                          <CheckCircle className={`h-4 w-4 ${
                            activity.status === 'success' ? 'text-green-600 dark:text-green-400' : ''
                          }`} />
                        ) : activity.status === 'warning' ? (
                          <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                        ) : activity.status === 'error' ? (
                          <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                        ) : (
                          <Activity className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-gray-900 dark:text-white">
                          {activity.message}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          {new Date(activity.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

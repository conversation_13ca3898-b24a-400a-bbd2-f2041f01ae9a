'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  ArrowLeft,
  Building,
  Users,
  Settings,
  Edit3,
  Plus,
  Shield,
  Mail,
  Phone,
  MapPin,
  Globe,
  Clock,
  UserCheck,
  UserX,
  MoreVertical,
  Key,
  Package,
  Grid3X3,
  Save,
  X,
  Check
} from 'lucide-react';
import DashboardLayout from '../../../../components/layout/DashboardLayout';
import MedicalLoader from '../../../../components/common/MedicalLoader';
import SearchInput from '../../../../components/common/SearchInput';
import ChoiceChips, { PermissionChips } from '../../../../components/common/ChoiceChips';
import { useAuth } from '../../../../contexts/AuthContext';
import { useThemeClasses, useClinicTheme } from '../../../../contexts/ThemeContext';
import { api, endpoints } from '@/lib/api';
import { cn } from '@/lib/utils';
import { clinicsApi, staffApi, rolesApi, permissionsApi, serviceCategoriesApi, servicesApi } from '../../../../lib/apiService';

export default function ViewClinicPage() {
  const router = useRouter();
  const params = useParams();
  const clinicId = params.id as string;
  const { hasRole } = useAuth();
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  
  // Check if user is admin or clinic owner
  const isAdmin = hasRole('admin');
  const isOwner = hasRole('clinic_owner');
  const isManager = hasRole('manager');
  const canManage = isAdmin || isOwner || isManager;

  const [clinic, setClinic] = useState<any>(null);
  const [staff, setStaff] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<any[]>([]);
  const [serviceCategories, setServiceCategories] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStaff, setSelectedStaff] = useState<any>(null);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [editingPermissions, setEditingPermissions] = useState(false);
  const [tempPermissions, setTempPermissions] = useState<string[]>([]);

  // Fetch clinic data from API
  useEffect(() => {
    const fetchClinicData = async () => {
      try {
        setLoading(true);

        // Fetch clinic details
        const clinicResponse = await api.get(endpoints.clinics.getById(clinicId));
        if (clinicResponse.data.success) {
          setClinic(clinicResponse.data.data);
        }

        // Fetch clinic staff
        const staffResponse = await api.get(endpoints.clinics.staff(clinicId));
        if (staffResponse.data.success) {
          setStaff(staffResponse.data.data);
        }

        // Fetch roles
        const rolesResponse = await api.get(endpoints.roles.list);
        if (rolesResponse.data.success) {
          setRoles(rolesResponse.data.data);
        }

        // Fetch permissions
        const permissionsResponse = await api.get(endpoints.permissions.list);
        if (permissionsResponse.data.success) {
          setPermissions(permissionsResponse.data.data);
        }

      } catch (error) {
        console.error('Error fetching clinic data:', error);
        setClinic(null);
        setStaff([]);
        setRoles([]);
        setPermissions([]);
      } finally {
        setLoading(false);
      }
    };

    if (clinicId) {
      fetchClinicData();
    }
  }, [clinicId]);





  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Use mock data service for offline-first functionality
        const [
          clinicData,
          staffData,
          rolesData,
          permissionsData,
          serviceCategoriesData,
          servicesData
        ] = await Promise.all([
          clinicsApi.getById(parseInt(clinicId)),
          staffApi.getAll({ clinic: parseInt(clinicId) }),
          rolesApi.getAll(),
          permissionsApi.getAll(),
          serviceCategoriesApi.getAll(),
          servicesApi.getAll()
        ]);

        setClinic(clinicData?.data || null);
        setStaff(staffData?.data || []);
        setRoles(rolesData?.data || []);
        setPermissions(permissionsData?.data || []);
        setServiceCategories(serviceCategoriesData?.data || []);
        setServices(servicesData?.data || []);
      } catch (error) {
        console.error('Error fetching clinic data:', error);
        // Set empty data instead of mock data
        setClinic(null);
        setStaff([]);
        setRoles([]);
        setPermissions([]);
        setServiceCategories([]);
        setServices([]);
      } finally {
        setIsLoading(false);
      }
    };

    if (clinicId && canManage) {
      fetchData();
    }
  }, [clinicId, canManage]);

  const handleSearch = (searchValue: string) => {
    setSearchTerm(searchValue);
  };

  const handleEditClinic = () => {
    router.push(`/clinics/edit/${clinicId}`);
  };

  const handleAddStaff = () => {
    router.push('/staff/add');
  };

  const handleEditStaff = (staffId: string) => {
    router.push(`/staff/edit/${staffId}`);
  };

  const handleToggleStaffStatus = async (staffId: string, isActive: boolean) => {
    // Update staff status
    setStaff(prev => prev.map(s => 
      s.id === parseInt(staffId) ? { ...s, isActive: !isActive } : s
    ));
  };

  const handleUpdateStaffRole = async (staffId: string, roleId: string) => {
    const role = roles.find(r => r.id === parseInt(roleId));
    if (role) {
      setStaff(prev => prev.map(s =>
        s.id === parseInt(staffId) ? { ...s, role } : s
      ));
    }
  };

  const handleStaffSelect = (staff: any) => {
    setSelectedStaff(staff);
    setTempPermissions(staff.permissions || []);
    setEditingPermissions(false);
  };

  const handleRoleSelect = (role: any) => {
    setSelectedRole(role);
  };

  const handlePermissionChange = (selectedPermissionIds: number[]) => {
    setTempPermissions(selectedPermissionIds);
  };

  const handleSavePermissions = async () => {
    if (!selectedStaff) return;

    try {
      // Update staff permissions
      setStaff(prev => prev.map(s =>
        s.id === selectedStaff.id
          ? { ...s, permissions: tempPermissions }
          : s
      ));

      // Update selected staff
      setSelectedStaff(prev => ({ ...prev, permissions: tempPermissions }));
      setEditingPermissions(false);

      // In real app, call API to save permissions
      // await staffApi.updatePermissions(selectedStaff.id, tempPermissions);
    } catch (error) {
      console.error('Error saving permissions:', error);
    }
  };

  const handleCancelPermissionEdit = () => {
    setTempPermissions(selectedStaff?.permissions || []);
    setEditingPermissions(false);
  };

  const filteredStaff = staff.filter(member => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      member.firstName.toLowerCase().includes(searchLower) ||
      member.lastName.toLowerCase().includes(searchLower) ||
      member.email.toLowerCase().includes(searchLower) ||
      member.role.displayName.toLowerCase().includes(searchLower)
    );
  });

  if (!canManage) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className={cn('text-xl font-semibold mb-2', themeClasses.text)}>Access Denied</h2>
            <p className={themeClasses.textSecondary}>You don't have permission to view clinic details.</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Loading clinic details..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={cn('min-h-screen p-6', themeClasses.bgSecondary)}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <button
              onClick={() => router.back()}
              className={cn('p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700', themeClasses.text)}
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div className="flex items-center gap-3 flex-1">
              <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                <Building className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className={cn('text-2xl font-bold', themeClasses.text)}>{clinic?.name}</h1>
                <p className={cn('text-sm', themeClasses.textSecondary)}>
                  Clinic Management Dashboard
                </p>
              </div>
            </div>
            {canManage && (
              <button
                onClick={handleEditClinic}
                className={cn(
                  'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors',
                  themeClasses.buttonSecondary
                )}
              >
                <Edit3 className="h-4 w-4" />
                Edit Clinic
              </button>
            )}
          </div>

          {/* Tabs */}
          <div className="flex flex-wrap gap-1 mb-8">
            {[
              { id: 'overview', label: 'Overview', icon: Building },
              { id: 'staff', label: 'Staff', icon: Users },
              { id: 'roles', label: 'Roles', icon: Shield },
              { id: 'permissions', label: 'Permissions', icon: Key },
              { id: 'serviceCategories', label: 'Service Categories', icon: Grid3X3 },
              { id: 'services', label: 'Services', icon: Package },
              { id: 'settings', label: 'Settings', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors',
                  activeTab === tab.id
                    ? 'text-white'
                    : themeClasses.buttonSecondary
                )}
                style={activeTab === tab.id ? { backgroundColor: colors.primary } : {}}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Staff</p>
                      <p className={cn('text-2xl font-bold', themeClasses.text)}>{clinic?.totalStaff}</p>
                    </div>
                    <Users className="h-8 w-8" style={{ color: colors.primary }} />
                  </div>
                </div>
                <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Patients</p>
                      <p className={cn('text-2xl font-bold', themeClasses.text)}>{clinic?.totalPatients}</p>
                    </div>
                    <UserCheck className="h-8 w-8 text-green-500" />
                  </div>
                </div>
                <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Clients</p>
                      <p className={cn('text-2xl font-bold', themeClasses.text)}>{clinic?.totalClients}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                </div>
              </div>

              {/* Clinic Information */}
              <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
                <h3 className={cn('text-lg font-semibold mb-4', themeClasses.text)}>Clinic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Mail className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className={cn('text-sm font-medium', themeClasses.text)}>Email</p>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>{clinic?.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className={cn('text-sm font-medium', themeClasses.text)}>Phone</p>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>{clinic?.phone}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className={cn('text-sm font-medium', themeClasses.text)}>Website</p>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>{clinic?.website}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                      <div>
                        <p className={cn('text-sm font-medium', themeClasses.text)}>Address</p>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>
                          {clinic?.address.street}<br />
                          {clinic?.address.city}, {clinic?.address.state} {clinic?.address.zipCode}<br />
                          {clinic?.address.country}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'staff' && (
            <div className="space-y-6">
              {/* Staff Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={cn('text-lg font-semibold', themeClasses.text)}>Staff Management</h3>
                  <p className={cn('text-sm', themeClasses.textSecondary)}>
                    Manage staff members, roles, and permissions
                  </p>
                </div>
                <button
                  onClick={handleAddStaff}
                  className={cn(
                    'flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white transition-colors'
                  )}
                  style={{ backgroundColor: colors.primary }}
                >
                  <Plus className="h-4 w-4" />
                  Add Staff
                </button>
              </div>

              {/* Search */}
              <div className={cn('p-4 rounded-xl border', themeClasses.card)}>
                <SearchInput
                  placeholder="Search staff by name, email, or role..."
                  onSearch={handleSearch}
                  initialValue={searchTerm}
                  className="w-full"
                />
              </div>

              {/* 3-Column Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Column 1: Staff Cards */}
                <div className="space-y-4">
                  <h4 className={cn('font-semibold', themeClasses.text)}>Staff Members</h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {filteredStaff.map((member) => (
                      <div
                        key={member.id}
                        className={cn(
                          'p-4 rounded-lg border cursor-pointer transition-all duration-200',
                          selectedStaff?.id === member.id
                            ? 'border-2 shadow-md'
                            : themeClasses.card,
                          'hover:shadow-md'
                        )}
                        style={selectedStaff?.id === member.id ? { borderColor: colors.primary } : {}}
                        onClick={() => handleStaffSelect(member)}
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-sm">
                            {member.firstName.charAt(0)}{member.lastName.charAt(0)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <h5 className={cn('font-medium truncate', themeClasses.text)}>
                              {member.firstName} {member.lastName}
                            </h5>
                            <p className={cn('text-xs truncate', themeClasses.textSecondary)}>{member.email}</p>
                            <div className="flex items-center gap-1 mt-1">
                              <span className={cn(
                                'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium',
                                member.isActive
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              )}>
                                {member.isActive ? 'Active' : 'Inactive'}
                              </span>
                              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {member.role.displayName}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Column 2: Roles List */}
                <div className="space-y-4">
                  <h4 className={cn('font-semibold', themeClasses.text)}>Available Roles</h4>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {roles.map((role) => (
                      <div
                        key={role.id}
                        className={cn(
                          'p-4 rounded-lg border cursor-pointer transition-all duration-200',
                          selectedRole?.id === role.id
                            ? 'border-2 shadow-md'
                            : themeClasses.card,
                          'hover:shadow-md'
                        )}
                        style={selectedRole?.id === role.id ? { borderColor: colors.primary } : {}}
                        onClick={() => handleRoleSelect(role)}
                      >
                        <div className="flex items-center gap-3">
                          <Shield className="h-8 w-8 text-blue-500" />
                          <div>
                            <h5 className={cn('font-medium', themeClasses.text)}>{role.displayName}</h5>
                            <p className={cn('text-xs', themeClasses.textSecondary)}>
                              {staff.filter(s => s.role.id === role.id).length} staff members
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Column 3: Permission Management */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className={cn('font-semibold', themeClasses.text)}>Permissions</h4>
                    {selectedStaff && canManage && (
                      <div className="flex gap-2">
                        {editingPermissions ? (
                          <>
                            <button
                              onClick={handleSavePermissions}
                              className={cn(
                                'flex items-center gap-1 px-3 py-1 rounded text-sm font-medium text-white'
                              )}
                              style={{ backgroundColor: colors.primary }}
                            >
                              <Save className="h-3 w-3" />
                              Save
                            </button>
                            <button
                              onClick={handleCancelPermissionEdit}
                              className={cn(
                                'flex items-center gap-1 px-3 py-1 rounded text-sm font-medium',
                                themeClasses.buttonSecondary
                              )}
                            >
                              <X className="h-3 w-3" />
                              Cancel
                            </button>
                          </>
                        ) : (
                          <button
                            onClick={() => setEditingPermissions(true)}
                            className={cn(
                              'flex items-center gap-1 px-3 py-1 rounded text-sm font-medium',
                              themeClasses.buttonSecondary
                            )}
                          >
                            <Edit3 className="h-3 w-3" />
                            Edit
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  {selectedStaff ? (
                    <div className={cn('p-4 rounded-lg border max-h-96 overflow-y-auto', themeClasses.card)}>
                      <div className="mb-4">
                        <h5 className={cn('font-medium', themeClasses.text)}>
                          {selectedStaff.firstName} {selectedStaff.lastName}
                        </h5>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>
                          {selectedStaff.role.displayName}
                        </p>
                      </div>

                      <PermissionChips
                        permissions={permissions}
                        selectedPermissions={editingPermissions ? tempPermissions : (selectedStaff.permissions || [])}
                        onSelectionChange={handlePermissionChange}
                        disabled={!editingPermissions}
                      />
                    </div>
                  ) : (
                    <div className={cn('p-8 rounded-lg border text-center', themeClasses.card)}>
                      <Users className="h-12 w-12 mx-auto mb-3 text-gray-400" />
                      <p className={cn('text-sm', themeClasses.textSecondary)}>
                        Select a staff member to view and manage their permissions
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={cn('text-lg font-semibold', themeClasses.text)}>Role Management</h3>
                  <p className={cn('text-sm', themeClasses.textSecondary)}>
                    Manage roles and their default permissions
                  </p>
                </div>
                {canManage && (
                  <button
                    className={cn(
                      'flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white transition-colors'
                    )}
                    style={{ backgroundColor: colors.primary }}
                  >
                    <Plus className="h-4 w-4" />
                    Add Role
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {roles.map((role) => (
                  <div key={role.id} className={cn('p-6 rounded-xl border', themeClasses.card)}>
                    <div className="flex items-center gap-3 mb-4">
                      <Shield className="h-8 w-8 text-blue-500" />
                      <div>
                        <h4 className={cn('font-semibold', themeClasses.text)}>{role.displayName}</h4>
                        <p className={cn('text-sm', themeClasses.textSecondary)}>
                          {staff.filter(s => s.role.id === role.id).length} staff members
                        </p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <p className={cn('text-sm font-medium', themeClasses.text)}>Default Permissions:</p>
                      <div className="flex flex-wrap gap-1">
                        {permissions.slice(0, 3).map((permission) => (
                          <span
                            key={permission.id}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                          >
                            {permission.name}
                          </span>
                        ))}
                        {permissions.length > 3 && (
                          <span className={cn('text-xs', themeClasses.textSecondary)}>
                            +{permissions.length - 3} more
                          </span>
                        )}
                      </div>
                    </div>

                    {canManage && (
                      <div className="flex gap-2 mt-4">
                        <button className={cn('flex-1 px-3 py-2 rounded text-sm font-medium', themeClasses.buttonSecondary)}>
                          <Edit3 className="h-3 w-3 inline mr-1" />
                          Edit
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={cn('text-lg font-semibold', themeClasses.text)}>Permission Management</h3>
                  <p className={cn('text-sm', themeClasses.textSecondary)}>
                    Manage system permissions and access controls
                  </p>
                </div>
                {canManage && (
                  <button
                    className={cn(
                      'flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white transition-colors'
                    )}
                    style={{ backgroundColor: colors.primary }}
                  >
                    <Plus className="h-4 w-4" />
                    Add Permission
                  </button>
                )}
              </div>

              <div className="space-y-6">
                {['staff', 'admin'].map((userType) => {
                  const typePermissions = permissions.filter(p => p.userType === userType);
                  return (
                    <div key={userType} className={cn('p-6 rounded-xl border', themeClasses.card)}>
                      <h4 className={cn('text-lg font-semibold mb-4 capitalize', themeClasses.text)}>
                        {userType} Permissions ({typePermissions.length})
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {typePermissions.map((permission) => (
                          <div key={permission.id} className={cn('p-4 rounded-lg border', themeClasses.cardSecondary)}>
                            <div className="flex items-center gap-2 mb-2">
                              <Key className="h-4 w-4 text-gray-500" />
                              <h5 className={cn('font-medium', themeClasses.text)}>{permission.name}</h5>
                            </div>
                            <p className={cn('text-sm', themeClasses.textSecondary)}>{permission.description}</p>
                            {canManage && (
                              <div className="flex gap-2 mt-3">
                                <button className={cn('px-2 py-1 rounded text-xs font-medium', themeClasses.buttonSecondary)}>
                                  Edit
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'serviceCategories' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={cn('text-lg font-semibold', themeClasses.text)}>Service Categories</h3>
                  <p className={cn('text-sm', themeClasses.textSecondary)}>
                    Manage service categories and their settings
                  </p>
                </div>
                {canManage && (
                  <button
                    className={cn(
                      'flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white transition-colors'
                    )}
                    style={{ backgroundColor: colors.primary }}
                  >
                    <Plus className="h-4 w-4" />
                    Add Category
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {serviceCategories.map((category) => (
                  <div key={category.id} className={cn('p-6 rounded-xl border', themeClasses.card)}>
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Grid3X3 className="h-8 w-8 text-purple-500" />
                        <div>
                          <h4 className={cn('font-semibold', themeClasses.text)}>{category.name}</h4>
                          <p className={cn('text-sm', themeClasses.textSecondary)}>
                            {services.filter(s => s.categoryId === category.id).length} services
                          </p>
                        </div>
                      </div>
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        category.isActive
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      )}>
                        {category.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <p className={cn('text-sm mb-4', themeClasses.textSecondary)}>{category.description}</p>

                    {canManage && (
                      <div className="flex gap-2">
                        <button className={cn('flex-1 px-3 py-2 rounded text-sm font-medium', themeClasses.buttonSecondary)}>
                          <Edit3 className="h-3 w-3 inline mr-1" />
                          Edit
                        </button>
                        <button
                          className={cn(
                            'px-3 py-2 rounded text-sm font-medium',
                            category.isActive
                              ? 'bg-red-100 text-red-800 hover:bg-red-200'
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          )}
                        >
                          {category.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'services' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className={cn('text-lg font-semibold', themeClasses.text)}>Services</h3>
                  <p className={cn('text-sm', themeClasses.textSecondary)}>
                    Manage individual services and pricing
                  </p>
                </div>
                {canManage && (
                  <button
                    className={cn(
                      'flex items-center gap-2 px-4 py-2 rounded-lg font-medium text-white transition-colors'
                    )}
                    style={{ backgroundColor: colors.primary }}
                  >
                    <Plus className="h-4 w-4" />
                    Add Service
                  </button>
                )}
              </div>

              <div className="space-y-6">
                {serviceCategories.filter(cat => cat.isActive).map((category) => {
                  const categoryServices = services.filter(s => s.categoryId === category.id);
                  return (
                    <div key={category.id} className={cn('p-6 rounded-xl border', themeClasses.card)}>
                      <h4 className={cn('text-lg font-semibold mb-4', themeClasses.text)}>
                        {category.name} ({categoryServices.length} services)
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {categoryServices.map((service) => (
                          <div key={service.id} className={cn('p-4 rounded-lg border', themeClasses.cardSecondary)}>
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Package className="h-4 w-4 text-gray-500" />
                                <h5 className={cn('font-medium', themeClasses.text)}>{service.name}</h5>
                              </div>
                              <span className={cn(
                                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                                service.isActive
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              )}>
                                {service.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                            <div className="space-y-1 mb-3">
                              <p className={cn('text-sm font-medium', themeClasses.text)}>
                                ${service.price}
                              </p>
                              <p className={cn('text-xs', themeClasses.textSecondary)}>
                                {service.duration} minutes
                              </p>
                            </div>
                            {canManage && (
                              <div className="flex gap-2">
                                <button className={cn('flex-1 px-2 py-1 rounded text-xs font-medium', themeClasses.buttonSecondary)}>
                                  Edit
                                </button>
                                <button
                                  className={cn(
                                    'px-2 py-1 rounded text-xs font-medium',
                                    service.isActive
                                      ? 'bg-red-100 text-red-800 hover:bg-red-200'
                                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                                  )}
                                >
                                  {service.isActive ? 'Disable' : 'Enable'}
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  Search,
  Plus,
  Edit3,
  Trash2,
  More<PERSON><PERSON><PERSON>,
  UserCheck,
  UserX,
  Shield,
  User
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import { api, endpoints } from '../../../lib/api';

interface StaffManagementTabProps {
  canEdit: boolean;
  isOwner: boolean;
  clinicId: string;
}

interface StaffMember {
  _id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
  role: string;
  permissions: string[];
  isActive: boolean;
  joinedAt: string;
}

interface Role {
  _id: string;
  name: string;
  displayName: string;
  description: string;
  level: number;
  permissions: string[];
}



export default function StaffManagementTab({ canEdit, isOwner, clinicId }: StaffManagementTabProps) {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStaff, setSelectedStaff] = useState<any>(null);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [showStaffModal, setShowStaffModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState<any[]>([]);
  const [roles, setRoles] = useState<any[]>([]);
  const [permissions, setPermissions] = useState<any[]>([]);

  useEffect(() => {
    loadStaffData();
    loadRolesAndPermissions();
  }, [clinicId]);

  const loadStaffData = async () => {
    try {
      setLoading(true);
      const response = await api.get(`${endpoints.staff.list}?clinic=${clinicId}`);
      if (response.data.success) {
        setStaff(response.data.data);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
      setStaff([]);
    } finally {
      setLoading(false);
    }
  };

  const loadRolesAndPermissions = async () => {
    try {
      // Load roles
      const rolesResponse = await api.get(endpoints.roles.list);
      if (rolesResponse.data.success) {
        setRoles(rolesResponse.data.data);
      } else {
        setRoles([]);
      }

      // Load permissions
      const permissionsResponse = await api.get(endpoints.permissions.list);
      if (permissionsResponse.data.success) {
        setPermissions(permissionsResponse.data.data);
      } else {
        setPermissions([]);
      }
    } catch (error) {
      console.error('Error loading roles and permissions:', error);
      setRoles([]);
      setPermissions([]);
    }
  };

  // Filter staff based on search
  const filteredStaff = staff.filter(staffMember => {
    const matchesSearch = !searchTerm ||
      staffMember.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staffMember.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staffMember.user.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  // Handle staff selection
  const handleStaffSelect = (staffMember: any) => {
    setSelectedStaff(staffMember);
    setSelectedRole(staffMember.role);
    setSelectedPermissions(staffMember.permissions?.map((p: any) => p._id) || []);
  };

  // Handle role selection
  const handleRoleSelect = (role: any) => {
    setSelectedRole(role);
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  // Get role badge color
  const getRoleBadgeColor = (roleName: string) => {
    switch (roleName) {
      case 'veterinarian': return 'bg-purple-100 text-purple-800';
      case 'vet_assistant': return 'bg-blue-100 text-blue-800';
      case 'manager': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Save staff changes
  const handleSaveStaffChanges = async () => {
    if (!selectedStaff || !selectedRole) return;

    try {
      const updateData = {
        role: selectedRole.id || selectedRole._id,
        permissions: selectedPermissions
      };

      const response = await api.put(endpoints.staff.update(selectedStaff.id || selectedStaff._id), updateData);

      if (response.data.success) {
        // Update local state
        setStaff(prevStaff =>
          prevStaff.map(s =>
            (s.id || s._id) === (selectedStaff.id || selectedStaff._id)
              ? { ...s, role: selectedRole, permissions: selectedPermissions.map(id => ({ _id: id, id })) }
              : s
          )
        );
        console.log('Staff member updated successfully');
      }
    } catch (error: any) {
      console.error('Failed to update staff member:', error);
      alert('Failed to update staff member. Please try again.');
    }
  };

  const handleCreateStaff = () => {
    setSelectedStaff(null);
    setSelectedRole(null);
    setSelectedPermissions([]);
    setModalMode('create');
    setShowStaffModal(true);
  };

  const handleEditStaffModal = (staffMember: any) => {
    setSelectedStaff(staffMember);
    setModalMode('edit');
    setShowStaffModal(true);
  };

  const handleDeleteStaff = async (staffId: string) => {
    if (!confirm('Are you sure you want to remove this staff member?')) {
      return;
    }

    try {
      const response = await api.delete(endpoints.staff.delete(staffId));

      if (response.data.success) {
        // Remove from local state
        setStaff(prevStaff => prevStaff.filter(s => (s.id || s._id) !== staffId));

        // Clear selection if deleted staff was selected
        if ((selectedStaff?.id || selectedStaff?._id) === staffId) {
          setSelectedStaff(null);
          setSelectedRole(null);
          setSelectedPermissions([]);
        }
        console.log('Staff member removed successfully');
      }
    } catch (error: any) {
      console.error('Failed to remove staff member:', error);
      alert('Failed to remove staff member. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading staff...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with search */}
      <div className="flex flex-col md:flex-row gap-4 items-center">
        <div className="relative max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search staff..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="flex-1"></div>

        {canEdit && (
          <button
            onClick={handleCreateStaff}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Staff
          </button>
        )}

        {selectedStaff && canEdit && (
          <button
            onClick={handleSaveStaffChanges}
            disabled={!selectedRole}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save Changes
          </button>
        )}
      </div>

      {/* Three Column Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 min-h-96">
        {/* Column 1: Staff List */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">Staff Members ({filteredStaff.length})</h3>
          </div>
          <div className="p-4">
            <div className="space-y-2">
              {filteredStaff.map(staffMember => {
                const staffId = staffMember.id || staffMember._id;
                const selectedId = selectedStaff?.id || selectedStaff?._id;

                return (
                  <div
                    key={staffId}
                    className={`p-3 rounded-md border cursor-pointer transition-colors ${
                      selectedId === staffId
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => handleStaffSelect(staffMember)}
                  >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                        {staffMember.user.avatar ? (
                          <img 
                            src={staffMember.user.avatar} 
                            alt={`${staffMember.user.firstName} ${staffMember.user.lastName}`}
                            className="h-8 w-8 rounded-full object-cover"
                          />
                        ) : (
                          `${staffMember.user.firstName.charAt(0)}${staffMember.user.lastName.charAt(0)}`
                        )}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {staffMember.user.firstName} {staffMember.user.lastName}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        {staffMember.user.email}
                      </p>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleBadgeColor(staffMember.role.name)}`}>
                        {staffMember.role.displayName}
                      </span>
                    </div>
                    {canEdit && (
                      <div className="flex-shrink-0">
                        <div className="relative">
                          <button
                            onClick={(e) => e.stopPropagation()}
                            className="p-1 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <MoreVertical className="h-4 w-4 text-gray-400" />
                          </button>
                          {/* Dropdown menu would go here */}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                );
              })}
              {filteredStaff.length === 0 && (
                <div className="p-4 text-center">
                  <p className="text-sm text-gray-500">
                    {searchTerm ? 'No staff members match your search' : 'No staff members found'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Column 2: Roles */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">Roles</h3>
            {selectedStaff && (
              <p className="text-xs text-gray-500 mt-1">
                Select role for {selectedStaff.user.firstName} {selectedStaff.user.lastName}
              </p>
            )}
          </div>
          <div className="p-4">
            {selectedStaff ? (
              <div className="space-y-2">
                {roles.map(role => {
                  const roleId = role.id || role._id;
                  const selectedRoleId = selectedRole?.id || selectedRole?._id;

                  return (
                    <div
                      key={roleId}
                      className={`p-3 rounded-md border cursor-pointer transition-colors ${
                        selectedRoleId === roleId
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                      onClick={() => handleRoleSelect(role)}
                    >
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">{role.displayName}</h4>
                      <span className="text-xs text-gray-500">Level {role.level}</span>
                    </div>
                    <p className="text-sm text-gray-600">{role.description}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {role.permissions?.length || 0} permissions
                    </p>
                  </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-4 text-center">
                <Shield className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">
                  Select a staff member to assign roles
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Column 3: Permissions */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-900">Permissions</h3>
            {selectedStaff && (
              <p className="text-xs text-gray-500 mt-1">
                Custom permissions for {selectedStaff.user.firstName} {selectedStaff.user.lastName}
              </p>
            )}
          </div>
          <div className="p-4">
            {selectedStaff ? (
              <div className="space-y-4">
                {['admin', 'staff', 'client', 'freelancer'].map(userType => {
                  const typePermissions = permissions.filter(p => p.userType === userType);
                  if (typePermissions.length === 0) return null;

                  return (
                    <div key={userType}>
                      <h4 className="text-sm font-medium text-gray-900 mb-2 capitalize">
                        {userType} Permissions
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {typePermissions.map(permission => {
                          const permissionId = permission.id || permission._id;

                          return (
                            <button
                              key={permissionId}
                              onClick={() => handlePermissionToggle(permissionId)}
                              className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium transition-colors ${
                                selectedPermissions.includes(permissionId)
                                  ? 'bg-blue-100 text-blue-800 border border-blue-200'
                                  : 'bg-gray-100 text-gray-800 border border-gray-200 hover:bg-gray-200'
                              }`}
                            >
                              {permission.name}
                              {selectedPermissions.includes(permissionId) && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handlePermissionToggle(permissionId);
                                  }}
                                  className="ml-1 text-blue-600 hover:text-blue-800"
                                >
                                  ×
                                </button>
                              )}
                            </button>
                          );
                        })}
                      </div>
                      {userType !== 'freelancer' && <hr className="mt-3 border-gray-200" />}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="p-4 text-center">
                <User className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">
                  Select a staff member to manage permissions
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Staff Modal would go here */}
      {showStaffModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <h3 className="text-lg font-medium text-gray-900">
                {modalMode === 'create' ? 'Add New Staff Member' : 'Edit Staff Member'}
              </h3>
              <div className="mt-4">
                <p className="text-sm text-gray-500">
                  Staff modal implementation will go here
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <button
                  onClick={() => setShowStaffModal(false)}
                  className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

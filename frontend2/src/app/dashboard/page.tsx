'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiHeart,
  FiCalendar,
  FiUsers,
  FiFileText,
  FiDollarSign,
  FiPackage,
  FiPlus,
  FiTrendingUp,
  FiActivity,
  FiClock,
  FiArrowUp,
  FiArrowDown
} from 'react-icons/fi';
import { cn, gradients } from '@/lib/utils';
import { Badge, Button, Card, CardBody, CardHeader, Text } from '@/components/ui';
import DashboardLayout from '../../components/layout/DashboardLayout';
import { useClinicIdentity } from '../../contexts/ClinicBrandingContext';
import { useTheme, useThemeClasses } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import { medicalColors, medicalComponents } from '../../utils/medicalTheme';
import { api, endpoints } from '@/lib/api';

const MotionDiv = motion.div;

interface DashboardStats {
  totalPatients: number;
  totalAppointments: number;
  todayAppointments: number;
  totalRevenue: number;
  staffCount: number;
  appointmentGrowth: number;
  revenueGrowth: number;
  patientGrowth: number;
  staffGrowth: number;
}

interface TodayAppointment {
  id: number;
  time: string;
  patient: {
    name: string;
    species: string;
  };
  owner: {
    firstName: string;
    lastName: string;
  };
  type: string;
  status: string;
}

export default function DashboardPage() {
  const { name: clinicName } = useClinicIdentity();
  const { actualTheme } = useTheme();
  const themeClasses = useThemeClasses();
  const { user, currentClinic } = useAuth();

  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [todayAppointments, setTodayAppointments] = useState<TodayAppointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [userName, setUserName] = useState('');

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!currentClinic?.id) return;

      try {
        setLoading(true);

        // Fetch analytics data for the current clinic
        const analyticsResponse = await api.get(`/api/analytics/dashboard?clinicId=${currentClinic.id}`);

        if (analyticsResponse.data.success) {
          const data = analyticsResponse.data.data;

          setStats({
            totalPatients: data.overview.totalPatients || 0,
            totalAppointments: data.overview.totalAppointments || 0,
            todayAppointments: data.overview.todayAppointments || 0,
            totalRevenue: data.overview.totalRevenue || 0,
            staffCount: currentClinic.stats?.staffCount || 0,
            appointmentGrowth: data.overview.appointmentGrowth || 0,
            revenueGrowth: data.overview.revenueGrowth || 0,
            patientGrowth: 0, // Could be calculated from historical data
            staffGrowth: 0 // Could be calculated from historical data
          });

          // Transform recent appointments
          if (data.recent?.appointments) {
            const transformedAppointments = data.recent.appointments.slice(0, 4).map((apt: any) => ({
              id: apt.id,
              time: new Date(`1970-01-01T${apt.scheduledTime}`).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              }),
              patient: {
                name: apt.patient?.name || 'Unknown',
                species: apt.patient?.species || 'Unknown'
              },
              owner: {
                firstName: apt.patient?.owner?.firstName || 'Unknown',
                lastName: apt.patient?.owner?.lastName || ''
              },
              type: apt.title || 'Appointment',
              status: apt.status || 'scheduled'
            }));
            setTodayAppointments(transformedAppointments);
          }
        }

        // Get user name for greeting
        if (user) {
          setUserName(`${user.firstName} ${user.lastName}`);
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set default values on error
        setStats({
          totalPatients: 0,
          totalAppointments: 0,
          todayAppointments: 0,
          totalRevenue: 0,
          staffCount: 0,
          appointmentGrowth: 0,
          revenueGrowth: 0,
          patientGrowth: 0,
          staffGrowth: 0
        });
        setTodayAppointments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [currentClinic?.id, user]);

  // Create stats array from fetched data
  const statsArray = stats ? [
    {
      label: 'Total Patients',
      value: stats.totalPatients.toLocaleString(),
      change: stats.patientGrowth,
      icon: FiHeart,
      color: 'blue'
    },
    {
      label: 'Appointments Today',
      value: stats.todayAppointments.toString(),
      change: stats.appointmentGrowth,
      icon: FiCalendar,
      color: 'green'
    },
    {
      label: 'Revenue This Month',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      change: stats.revenueGrowth,
      icon: FiDollarSign,
      color: 'purple'
    },
    {
      label: 'Active Staff',
      value: stats.staffCount.toString(),
      change: stats.staffGrowth,
      icon: FiUsers,
      color: 'orange'
    },
  ] : [];

  const quickActions = [
    { icon: FiHeart, label: 'Add New Patient', color: medicalColors.medicalPink },
    { icon: FiCalendar, label: 'Schedule Appointment', color: medicalColors.medicalBlue },
    { icon: FiFileText, label: 'Create Medical Record', color: medicalColors.medicalTeal },
    { icon: FiDollarSign, label: 'Generate Invoice', color: medicalColors.medicalGreen },
    { icon: FiPackage, label: 'Update Inventory', color: medicalColors.medicalPurple },
  ];

  return (
    <DashboardLayout>
      <div
        className={cn(
          'min-h-screen relative p-6 md:p-8 lg:p-12',
          actualTheme === 'dark'
            ? 'bg-gradient-to-br from-gray-900 via-blue-900/20 to-pink-900/20'
            : 'bg-gradient-to-br from-blue-50 via-pink-50 to-white'
        )}
        style={{
          backgroundImage: actualTheme === 'dark'
            ? `
              radial-gradient(circle at 20% 50%, rgba(125, 211, 252, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(253, 164, 175, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(77, 208, 225, 0.1) 0%, transparent 50%)
            `
            : `
              radial-gradient(circle at 20% 50%, rgba(125, 211, 252, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(253, 164, 175, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 80%, rgba(77, 208, 225, 0.3) 0%, transparent 50%)
            `,
        }}
      >
        {/* Welcome Section */}
        <MotionDiv
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="mb-12 relative z-10"
        >
          <div className="flex flex-col gap-4 items-start">
            <MotionDiv
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h1 className={cn(
                'text-4xl md:text-5xl font-bold tracking-tight',
                'bg-gradient-to-r from-primary-400 via-purple-400 to-pink-400',
                'bg-clip-text text-transparent'
              )}>
                Good morning, {userName || 'Doctor'}! ✨
              </h1>
            </MotionDiv>
            <MotionDiv
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <p className={cn('text-lg font-medium opacity-80', themeClasses.textSecondary)}>
                Here's what's happening at {clinicName} today.
              </p>
            </MotionDiv>
          </div>
        </MotionDiv>

        {/* Stats Grid */}
        <MotionDiv
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mb-12 relative z-10"
        >
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[1, 2, 3, 4].map((index) => (
                <div key={index} className="p-8 rounded-xl bg-white/70 backdrop-blur-sm border border-white/30 animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {statsArray.map((stat, index) => (
              <MotionDiv
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 0.8 + (index * 0.1),
                  ease: "easeOut"
                }}
                whileHover={{
                  y: -8,
                  transition: { duration: 0.3 }
                }}
              >
                <Card
                  variant="soul"
                  className={cn(
                    'p-8 relative overflow-hidden',
                    'border-white/20 shadow-large backdrop-blur-xl',
                    'hover:shadow-2xl transition-all duration-300'
                  )}
                >
                  {/* Top gradient bar */}
                  <div
                    className={cn(
                      'absolute top-0 left-0 right-0 h-1',
                      stat.color === 'blue' && 'bg-gradient-to-r from-blue-400 to-blue-600',
                      stat.color === 'green' && 'bg-gradient-to-r from-green-400 to-green-600',
                      stat.color === 'purple' && 'bg-gradient-to-r from-purple-400 to-purple-600',
                      stat.color === 'orange' && 'bg-gradient-to-r from-orange-400 to-orange-600'
                    )}
                  />

                  <div className="flex justify-between items-start">
                    <div className="flex flex-col gap-3">
                      <div>
                        <p className="text-xs font-medium text-gray-600 uppercase tracking-wide">
                          {stat.label}
                        </p>
                        <p className={cn(
                          'text-2xl font-bold bg-gradient-to-r bg-clip-text text-transparent',
                          stat.color === 'blue' && 'from-blue-500 to-blue-600',
                          stat.color === 'green' && 'from-green-500 to-green-600',
                          stat.color === 'purple' && 'from-purple-500 to-purple-600',
                          stat.color === 'orange' && 'from-orange-500 to-orange-600'
                        )}>
                          {stat.value}
                        </p>
                        <div className="flex items-center gap-1 text-xs font-medium">
                          {stat.change >= 0 ? (
                            <FiArrowUp className="w-3 h-3 text-green-500" />
                          ) : (
                            <FiArrowDown className="w-3 h-3 text-red-500" />
                          )}
                          <span className={stat.change >= 0 ? 'text-green-600' : 'text-red-600'}>
                            {Math.abs(stat.change)}% from last month
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className={cn(
                      'p-4 rounded-2xl shadow-glow',
                      stat.color === 'blue' && 'bg-gradient-to-br from-blue-400 to-blue-600',
                      stat.color === 'green' && 'bg-gradient-to-br from-green-400 to-green-600',
                      stat.color === 'purple' && 'bg-gradient-to-br from-purple-400 to-purple-600',
                      stat.color === 'orange' && 'bg-gradient-to-br from-orange-400 to-orange-600'
                    )}>
                      <stat.icon className="w-8 h-8 text-white" />
                    </div>
                  </div>
                </Card>
              </MotionDiv>
            ))}
          </div>
        </MotionDiv>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Today's Appointments */}
          <div className="lg:col-span-2">
            <MotionDiv
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.2 }}
              whileHover={{ y: -4 }}
            >
              <Card variant="soul" className="overflow-hidden">
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-purple-400" />

                <CardHeader className="border-b border-white/10">
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-bold">Today's Appointments</h3>
                      <p className="text-xs text-gray-600 opacity-80">
                        {recentAppointments.length} appointments scheduled
                      </p>
                    </div>
                    <MotionDiv
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant="primary"
                        size="lg"
                        leftIcon={<FiPlus />}
                        className="bg-gradient-to-br from-blue-400 to-purple-500 shadow-glow"
                      >
                        New Appointment
                      </Button>
                    </MotionDiv>
                  </div>
                </CardHeader>

                <CardBody>
                  <div className="flex flex-col gap-6">
                    {recentAppointments.map((appointment, index) => (
                      <MotionDiv
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 1.4 + (index * 0.1) }}
                        whileHover={{ x: 4, transition: { duration: 0.2 } }}
                      >
                        <div className="p-6 bg-white/70 backdrop-blur-sm rounded-2xl border border-white/30 cursor-pointer transition-all duration-300 hover:bg-white/90 hover:border-blue-200 hover:shadow-medium">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center gap-5">
                              <div className="relative">
                                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 font-semibold border-3 border-white shadow-medium">
                                  {appointment.patient.charAt(0)}
                                </div>
                                <div className="absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-green-400 rounded-full border-2 border-white" />
                              </div>
                              <div>
                                <p className="font-bold text-md text-gray-800">
                                  {appointment.patient}
                                </p>
                                <p className="text-xs text-gray-600 opacity-80">
                                  Owner: {appointment.owner}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-md text-blue-600">
                                {appointment.time}
                              </p>
                              <Badge variant="primary" size="sm">
                                {appointment.type}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </MotionDiv>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </MotionDiv>
          </div>

          {/* Quick Actions */}
          <div>
            <MotionDiv
              initial={{ opacity: 0, y: 40 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.4 }}
              whileHover={{ y: -4 }}
            >
              <Card variant="soul" className="overflow-hidden">
                <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-400 to-pink-400" />

                <CardHeader className="border-b border-white/10">
                  <div>
                    <h3 className="text-lg font-bold">Quick Actions</h3>
                    <p className="text-xs text-gray-600 opacity-80">
                      Streamline your workflow
                    </p>
                  </div>
                </CardHeader>

                <CardBody>
                  <div className="flex flex-col gap-4">
                    {quickActions.map((action, index) => (
                      <MotionDiv
                        key={index}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 1.6 + (index * 0.1) }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <button className={cn(
                          'w-full p-5 rounded-2xl text-left transition-all duration-300',
                          'hover:translate-x-1 hover:shadow-medium',
                          action.color === 'blue' && 'hover:bg-blue-50 hover:border-blue-200',
                          action.color === 'green' && 'hover:bg-green-50 hover:border-green-200',
                          action.color === 'purple' && 'hover:bg-purple-50 hover:border-purple-200',
                          action.color === 'orange' && 'hover:bg-orange-50 hover:border-orange-200',
                          action.color === 'red' && 'hover:bg-red-50 hover:border-red-200'
                        )}>
                          <div className="flex items-center gap-4 w-full">
                            <div className={cn(
                              'p-3 rounded-xl shadow-medium',
                              action.color === 'blue' && 'bg-gradient-to-br from-blue-400 to-blue-600',
                              action.color === 'green' && 'bg-gradient-to-br from-green-400 to-green-600',
                              action.color === 'purple' && 'bg-gradient-to-br from-purple-400 to-purple-600',
                              action.color === 'orange' && 'bg-gradient-to-br from-orange-400 to-orange-600',
                              action.color === 'red' && 'bg-gradient-to-br from-red-400 to-red-600'
                            )}>
                              <action.icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1 text-left">
                              <p className="font-bold text-sm text-gray-800">
                                {action.label}
                              </p>
                              <p className="text-xs text-gray-600 opacity-70">
                                Click to get started
                              </p>
                            </div>
                          </div>
                        </button>
                      </MotionDiv>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </MotionDiv>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

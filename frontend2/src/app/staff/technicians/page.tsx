'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  Plus,
  Search,
  Filter,
  Edit3,
  Trash2,
  MoreVertical,
  UserCheck,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  Award,
  Activity,
  GraduationCap,
  Star,
  Clock,
  Microscope,
  Syringe,
  Heart
} from 'lucide-react';
import DashboardLayout from '../../../components/layout/DashboardLayout';
import Table, { TableColumn, PaginationInfo } from '../../../components/common/Table';
import ActionDropdown, { ActionItem } from '../../../components/common/ActionDropdown';
import MedicalLoader from '../../../components/common/MedicalLoader';
import { useThemeClasses, useClinicTheme } from '../../../contexts/ThemeContext';
import { useAuth } from '../../../contexts/AuthContext';
import { api, endpoints } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Technician {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  isActive: boolean;
  joinedAt: string;
  specializations: string[];
  certifications: string[];
  licenseNumber: string;
  yearsExperience: number;
  rating: number;
  totalAssists: number;
  monthlyHours: number;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact: {
    name: string;
    relationship: string;
    phone: string;
  };
  schedule: {
    monday: { start: string; end: string; isWorking: boolean };
    tuesday: { start: string; end: string; isWorking: boolean };
    wednesday: { start: string; end: string; isWorking: boolean };
    thursday: { start: string; end: string; isWorking: boolean };
    friday: { start: string; end: string; isWorking: boolean };
    saturday: { start: string; end: string; isWorking: boolean };
    sunday: { start: string; end: string; isWorking: boolean };
  };
  hourlyRate: number;
  notes?: string;
}

export default function TechniciansPage() {
  const themeClasses = useThemeClasses();
  const { colors } = useClinicTheme();
  const { user, currentClinic } = useAuth();

  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedTech, setSelectedTech] = useState<Technician | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // Fetch technicians data from API
  useEffect(() => {
    const fetchTechnicians = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const queryParams = new URLSearchParams({
          page: pagination.page.toString(),
          limit: pagination.limit.toString(),
          role: 'technician', // Filter for technicians only
          ...(searchTerm && { search: searchTerm }),
          ...(filterStatus !== 'all' && { status: filterStatus }),
          ...(currentClinic?.id && { clinicId: currentClinic.id.toString() })
        });

        // Fetch staff with technician role
        const response = await api.get(`/api/staff?${queryParams}`);

        if (response.data.success) {
          // Transform API data to match UI interface
          const transformedTechnicians: Technician[] = response.data.data.map((tech: any) => ({
            id: tech.id,
            firstName: tech.user?.firstName || '',
            lastName: tech.user?.lastName || '',
            email: tech.user?.email || '',
            phone: tech.user?.phone || '',
            avatar: tech.user?.avatar,
            isActive: tech.isActive !== false,
            joinedAt: tech.createdAt || new Date().toISOString(),
            specializations: tech.specializations || [],
            certifications: tech.certifications || [],
            licenseNumber: tech.licenseNumber || '',
            yearsExperience: tech.yearsExperience || 0,
            rating: tech.rating || 0,
            totalAssists: tech.totalAssists || 0,
            monthlyHours: tech.monthlyHours || 0,
            address: tech.address || {
              street: '',
              city: '',
              state: '',
              zipCode: '',
              country: ''
            },
            emergencyContact: tech.emergencyContact || {
              name: '',
              relationship: '',
              phone: ''
            },
            schedule: tech.schedule || {
              monday: { start: '08:00', end: '17:00', isWorking: true },
              tuesday: { start: '08:00', end: '17:00', isWorking: true },
              wednesday: { start: '08:00', end: '17:00', isWorking: true },
              thursday: { start: '08:00', end: '17:00', isWorking: true },
              friday: { start: '08:00', end: '17:00', isWorking: true },
              saturday: { start: '08:00', end: '12:00', isWorking: false },
              sunday: { start: '00:00', end: '00:00', isWorking: false }
            },
            hourlyRate: tech.hourlyRate || 0,
            notes: tech.notes || ''
          }));

          setTechnicians(transformedTechnicians);

          // Update pagination if provided
          if (response.data.pagination) {
            setPagination({
              page: response.data.pagination.page || 1,
              limit: response.data.pagination.limit || 10,
              total: response.data.pagination.total || 0,
              totalPages: response.data.pagination.pages || 0
            });
          }
        }
      } catch (error) {
        console.error('Error fetching technicians:', error);
        // Set empty array on error
        setTechnicians([]);
      } finally {
        setLoading(false);
      }
    };

    fetchTechnicians();
  }, [pagination.page, pagination.limit, searchTerm, filterStatus, currentClinic?.id]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm.length >= 3 || searchTerm.length === 0) {
        // Reset to first page when searching
        setPagination(prev => ({ ...prev, page: 1 }));
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Remove the mock data array that was here before





  const filteredTechnicians = technicians.filter(tech => {
    const matchesSearch = !searchTerm ||
      tech.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tech.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tech.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tech.specializations.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'active' && tech.isActive) ||
      (filterStatus === 'inactive' && !tech.isActive);

    return matchesSearch && matchesStatus;
  });

  const columns: TableColumn[] = [
    {
      key: 'name',
      label: 'Technician',
      sortable: true,
      render: (_, tech: Technician) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center text-white font-semibold">
            {tech.avatar ? (
              <img src={tech.avatar} alt={`${tech.firstName} ${tech.lastName}`} className="w-10 h-10 rounded-full object-cover" />
            ) : (
              `${tech.firstName.charAt(0)}${tech.lastName.charAt(0)}`
            )}
          </div>
          <div>
            <div className={cn('font-medium', themeClasses.text)}>
              {tech.firstName} {tech.lastName}
            </div>
            <div className={cn('text-sm', themeClasses.textSecondary)}>
              License: {tech.licenseNumber}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      label: 'Contact',
      render: (_, tech: Technician) => (
        <div className="space-y-1">
          <div className={cn('text-sm', themeClasses.text)}>{tech.email}</div>
          <div className={cn('text-sm', themeClasses.textSecondary)}>{tech.phone}</div>
        </div>
      )
    },
    {
      key: 'specializations',
      label: 'Specializations',
      render: (_, tech: Technician) => (
        <div className="flex flex-wrap gap-1">
          {tech.specializations.slice(0, 2).map((spec, index) => (
            <span
              key={index}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              {spec}
            </span>
          ))}
          {tech.specializations.length > 2 && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
              +{tech.specializations.length - 2} more
            </span>
          )}
        </div>
      )
    },
    {
      key: 'experience',
      label: 'Experience',
      sortable: true,
      render: (_, tech: Technician) => (
        <div className="space-y-1">
          <div className={cn('text-sm font-medium', themeClasses.text)}>
            {tech.yearsExperience} years
          </div>
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className={cn('text-sm', themeClasses.textSecondary)}>
              {tech.rating}/5.0
            </span>
          </div>
        </div>
      )
    },
    {
      key: 'performance',
      label: 'Performance',
      sortable: true,
      render: (_, tech: Technician) => (
        <div className="space-y-1">
          <div className={cn('text-sm font-medium', themeClasses.text)}>
            {tech.totalAssists.toLocaleString()} assists
          </div>
          <div className={cn('text-xs', themeClasses.textSecondary)}>
            {tech.monthlyHours}h/month
          </div>
        </div>
      )
    },
    {
      key: 'rate',
      label: 'Hourly Rate',
      sortable: true,
      render: (_, tech: Technician) => (
        <div className={cn('text-sm font-medium', themeClasses.text)}>
          ${tech.hourlyRate}/hr
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (_, tech: Technician) => (
        <span
          className={cn(
            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
            tech.isActive
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
          )}
        >
          {tech.isActive ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ];

  const actionItems: ActionItem[] = [
    {
      label: 'View Details',
      icon: UserCheck,
      onClick: (tech: Technician) => {
        setSelectedTech(tech);
        setShowDetailsModal(true);
      }
    },
    {
      label: 'Edit',
      icon: Edit3,
      onClick: (tech: Technician) => {
        setSelectedTech(tech);
        setShowAddModal(true);
      }
    },
    {
      label: 'Deactivate',
      icon: Trash2,
      onClick: (tech: Technician) => {
        // Handle deactivation
        console.log('Deactivate technician:', tech.id);
      },
      variant: 'danger'
    }
  ];

  const paginationInfo: PaginationInfo = {
    currentPage: 1,
    totalPages: Math.ceil(filteredTechnicians.length / 10),
    totalItems: filteredTechnicians.length,
    itemsPerPage: 10
  };

  if (loading) {
    return (
      <DashboardLayout>
        <MedicalLoader
          message="Loading technicians..."
          size="lg"
          fullScreen
        />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className={cn('min-h-screen p-6', themeClasses.bgSecondary)}>
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className={cn('text-3xl font-bold', themeClasses.text)}>Veterinary Technicians</h1>
              <p className={themeClasses.textSecondary}>
                Manage veterinary technician staff, certifications, and schedules
              </p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200',
                'text-white hover:shadow-lg transform hover:-translate-y-0.5'
              )}
              style={{ backgroundColor: colors.primary }}
            >
              <Plus className="h-5 w-5" />
              Add Technician
            </button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Technicians</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>{technicians.length}</p>
                </div>
                <Microscope className="h-8 w-8" style={{ color: colors.primary }} />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Active</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {technicians.filter(t => t.isActive).length}
                  </p>
                </div>
                <UserCheck className="h-8 w-8 text-green-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Avg Experience</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {Math.round(technicians.reduce((acc, t) => acc + t.yearsExperience, 0) / technicians.length || 0)} years
                  </p>
                </div>
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
            <div className={cn('p-6 rounded-xl border', themeClasses.card)}>
              <div className="flex items-center justify-between">
                <div>
                  <p className={cn('text-sm font-medium', themeClasses.textSecondary)}>Total Assists</p>
                  <p className={cn('text-2xl font-bold', themeClasses.text)}>
                    {technicians.reduce((acc, t) => acc + t.totalAssists, 0).toLocaleString()}
                  </p>
                </div>
                <Heart className="h-8 w-8 text-red-500" />
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className={cn('p-6 rounded-xl border mb-6', themeClasses.card)}>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search technicians by name, email, or specialization..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={cn(
                      'w-full pl-10 pr-4 py-3 rounded-lg border transition-all duration-200',
                      themeClasses.input,
                      themeClasses.inputFocus
                    )}
                  />
                </div>
              </div>
              <div className="flex items-center gap-4">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className={cn(
                    'px-3 py-2 rounded-lg border transition-all duration-200',
                    themeClasses.input,
                    themeClasses.inputFocus
                  )}
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
          </div>

          {/* Table */}
          <Table
            data={filteredTechnicians}
            columns={columns}
            pagination={paginationInfo}
            actions={actionItems}
            onPageChange={(page) => console.log('Page changed:', page)}
            onSort={(column, direction) => console.log('Sort:', column, direction)}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Plus,
  Eye,
  Download,
  Calendar,
  DollarSign,
  User,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

import DashboardLayout from '../../../components/layout/DashboardLayout';
import Table, { TableColumn, PaginationInfo } from '../../../components/common/Table';
import ActionDropdown, { ActionItem } from '../../../components/common/ActionDropdown';
import { useAuth } from '../../../contexts/AuthContext';
import { useThemeClasses, useClinicTheme } from '../../../contexts/ThemeContext';
import { api, endpoints } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Payment {
  id: number;
  _id: string;
  paymentNumber: string;
  invoice: {
    invoiceNumber: string;
    totalAmount: number;
  };
  client: {
    firstName: string;
    lastName: string;
    email: string;
  };
  amount: number;
  paymentMethod: 'Cash' | 'Credit Card' | 'Debit Card' | 'Check' | 'Bank Transfer' | 'Online';
  transactionId?: string;
  status: 'Completed' | 'Pending' | 'Failed' | 'Refunded';
  paymentDate: string;
  notes?: string;
  processedBy: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
}

export default function PaymentsPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const themeClasses = useThemeClasses();
  const { isDark, colors } = useClinicTheme();
  
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationInfo | undefined>();
  const [searchValue, setSearchValue] = useState('');
  const [sortBy, setSortBy] = useState('paymentDate');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filters, setFilters] = useState({
    status: '',
    paymentMethod: '',
    dateRange: ''
  });

  // Fetch real payments data from API
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const queryParams = new URLSearchParams({
          page: pagination?.page?.toString() || '1',
          limit: pagination?.limit?.toString() || '10',
          sortBy,
          sortOrder,
          ...(searchValue && { search: searchValue }),
          ...(filters.status && { status: filters.status }),
          ...(filters.paymentMethod && { paymentMethod: filters.paymentMethod }),
          ...(filters.dateRange && { dateRange: filters.dateRange })
        });

        // Fetch payments from API
        const response = await api.get(`/api/invoices/payments?${queryParams}`);

        if (response.data.success) {
          // Transform API data to match UI interface
          const transformedPayments: Payment[] = response.data.data.map((payment: any) => ({
            id: payment.id,
            _id: payment._id,
            paymentNumber: payment.paymentNumber,
            invoice: {
              invoiceNumber: payment.invoice?.invoiceNumber || 'N/A',
              totalAmount: payment.invoice?.totalAmount || 0
            },
            client: {
              firstName: payment.client?.firstName || 'Unknown',
              lastName: payment.client?.lastName || '',
              email: payment.client?.email || ''
            },
            amount: payment.amount,
            paymentMethod: payment.paymentMethod,
            transactionId: payment.transactionId || '',
            status: payment.status,
            paymentDate: payment.paymentDate,
            processedBy: payment.processedBy ? {
              firstName: payment.processedBy.firstName || '',
              lastName: payment.processedBy.lastName || ''
            } : undefined,
            notes: payment.notes || '',
            createdAt: payment.createdAt
          }));

          setPayments(transformedPayments);

          // Update pagination if provided
          if (response.data.pagination) {
            setPagination({
              page: response.data.pagination.page || 1,
              limit: response.data.pagination.limit || 10,
              total: response.data.pagination.total || 0,
              totalPages: response.data.pagination.pages || 0
            });
          }
        }
      } catch (error) {
        console.error('Error fetching payments:', error);
        // Set empty array on error
        setPayments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchPayments();
  }, [pagination?.page, pagination?.limit, sortBy, sortOrder, searchValue, filters]);

  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchValue.length >= 3 || searchValue.length === 0) {
        // Reset to first page when searching
        setPagination(prev => ({ ...prev, page: 1 }));
      }
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [searchValue]);



  // Handle search
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // Handle sort
  const handleSort = (column: string, order: 'asc' | 'desc') => {
    setSortBy(column);
    setSortOrder(order);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    // fetchPayments(page, pagination?.itemsPerPage || 10);
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    // fetchPayments(1, size);
  };

  // Define table columns
  const columns: TableColumn[] = [
    {
      key: 'paymentNumber',
      label: 'Payment #',
      sortable: true,
      render: (value: string, row: Payment) => (
        <div className="font-medium text-blue-600 dark:text-blue-400">
          {value}
        </div>
      )
    },
    {
      key: 'invoice',
      label: 'Invoice',
      sortable: false,
      render: (value: any) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">
            {value.invoiceNumber}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total: ${value.totalAmount}
          </div>
        </div>
      )
    },
    {
      key: 'client',
      label: 'Client',
      sortable: false,
      render: (value: any) => (
        <div>
          <div className="font-medium text-gray-900 dark:text-white">
            {value.firstName} {value.lastName}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {value.email}
          </div>
        </div>
      )
    },
    {
      key: 'amount',
      label: 'Amount',
      sortable: true,
      render: (value: number) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="h-4 w-4 text-gray-400" />
          <span className="font-medium text-gray-900 dark:text-white">
            {value.toFixed(2)}
          </span>
        </div>
      )
    },
    {
      key: 'paymentMethod',
      label: 'Method',
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          <CreditCard className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-900 dark:text-white">
            {value}
          </span>
        </div>
      )
    },
    {
      key: 'paymentDate',
      label: 'Payment Date',
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center space-x-1">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-900 dark:text-white">
            {new Date(value).toLocaleDateString()}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value: string) => {
        const getStatusIcon = () => {
          switch (value) {
            case 'Completed':
              return <CheckCircle className="h-4 w-4" />;
            case 'Failed':
              return <XCircle className="h-4 w-4" />;
            case 'Pending':
              return <Clock className="h-4 w-4" />;
            case 'Refunded':
              return <RefreshCw className="h-4 w-4" />;
            default:
              return null;
          }
        };

        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            value === 'Completed' 
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
              : value === 'Failed'
              ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
              : value === 'Pending'
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
              : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
          }`}>
            <span className="mr-1">{getStatusIcon()}</span>
            {value}
          </span>
        );
      }
    },
    {
      key: 'processedBy',
      label: 'Processed By',
      sortable: false,
      render: (value: any) => (
        <div className="text-sm text-gray-900 dark:text-white">
          {value.firstName} {value.lastName}
        </div>
      )
    }
  ];

  // Define actions for each row
  const getActions = (payment: Payment): ActionItem[] => [
    {
      label: 'View Payment',
      icon: Eye,
      onClick: () => {
        console.log('View payment:', payment.id);
      }
    },
    {
      label: 'Download Receipt',
      icon: Download,
      onClick: () => {
        console.log('Download receipt:', payment.id);
      }
    },
    ...(payment.status === 'Completed' ? [{
      label: 'Process Refund',
      icon: RefreshCw,
      onClick: () => {
        console.log('Process refund:', payment.id);
      },
      divider: true
    }] : [])
  ];

  // Show loading state
  if (authLoading) {
    return (
      <DashboardLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-6">
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div
        className={cn("min-h-screen p-6", themeClasses.bgSecondary)}
        style={{
          background: isDark
            ? `linear-gradient(135deg, ${colors.primary}05, ${colors.secondary}03), ${themeClasses.bgSecondary.replace('bg-', '')}`
            : `linear-gradient(135deg, ${colors.primary}08, ${colors.secondary}05), ${themeClasses.bgSecondary.replace('bg-', '')}`
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h1 className={cn("text-2xl font-bold", themeClasses.text)}>
                  Payments
                </h1>
                <p className={themeClasses.textSecondary}>
                  Track and manage all payment transactions
                </p>
              </div>
              <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Plus className="h-4 w-4 mr-2" />
                Record Payment
              </button>
            </div>

            {/* Filters */}
            <div className={cn("p-4 rounded-lg", themeClasses.card)}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Statuses</option>
                    <option value="Completed">Completed</option>
                    <option value="Pending">Pending</option>
                    <option value="Failed">Failed</option>
                    <option value="Refunded">Refunded</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Payment Method
                  </label>
                  <select
                    value={filters.paymentMethod}
                    onChange={(e) => setFilters(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Methods</option>
                    <option value="Cash">Cash</option>
                    <option value="Credit Card">Credit Card</option>
                    <option value="Debit Card">Debit Card</option>
                    <option value="Check">Check</option>
                    <option value="Bank Transfer">Bank Transfer</option>
                    <option value="Online">Online</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Date Range
                  </label>
                  <select
                    value={filters.dateRange}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                    <option value="year">This Year</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Table */}
            <Table
              data={payments}
              columns={columns}
              excludedFields={['_id', '__v', 'transactionId', 'notes']}
              pagination={pagination}
              loading={loading}
              searchValue={searchValue}
              sortBy={sortBy}
              sortOrder={sortOrder}
              onSearch={handleSearch}
              onSort={handleSort}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              actions={(row) => <ActionDropdown actions={getActions(row)} />}
              emptyMessage="No payments found"
              showSearch={true}
              showPagination={true}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

'use client';

import { api } from './api';

// Offline-first data service with local storage and sync capabilities
class OfflineDataService {
  private storagePrefix = 'vetcare_';
  private syncQueue: any[] = [];
  private isOnline = typeof window !== 'undefined' ? navigator.onLine : true;

  constructor() {
    if (typeof window !== 'undefined') {
      // Listen for online/offline events
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
      
      // Initialize sync queue from localStorage
      this.loadSyncQueue();
    }
  }

  // Storage operations
  private getStorageKey(key: string): string {
    return `${this.storagePrefix}${key}`;
  }

  private setItem(key: string, data: any): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(this.getStorageKey(key), JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  private getItem(key: string): any {
    if (typeof window === 'undefined') return null;
    try {
      const item = localStorage.getItem(this.getStorageKey(key));
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return null;
    }
  }

  private removeItem(key: string): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.getStorageKey(key));
  }

  // Sync queue management
  private loadSyncQueue(): void {
    this.syncQueue = this.getItem('sync_queue') || [];
  }

  private saveSyncQueue(): void {
    this.setItem('sync_queue', this.syncQueue);
  }

  private addToSyncQueue(operation: any): void {
    this.syncQueue.push({
      ...operation,
      timestamp: Date.now(),
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    });
    this.saveSyncQueue();
  }

  // Network status handlers
  private handleOnline(): void {
    this.isOnline = true;
    console.log('App is online - starting sync...');
    this.syncPendingOperations();
  }

  private handleOffline(): void {
    this.isOnline = false;
    console.log('App is offline - operations will be queued');
  }

  // Data operations
  async getData(key: string, fallbackData?: any): Promise<any> {
    // Try to get from localStorage first
    const localData = this.getItem(key);
    
    if (localData) {
      return localData;
    }
    
    // If online, try to fetch from API
    if (this.isOnline) {
      try {
        // This would be replaced with actual API calls
        const apiData = await this.fetchFromAPI(key);
        if (apiData) {
          this.setItem(key, apiData);
          return apiData;
        }
      } catch (error) {
        console.error('Error fetching from API:', error);
      }
    }
    
    // Return fallback data if provided
    return fallbackData || null;
  }

  async setData(key: string, data: any, syncToServer = true): Promise<void> {
    // Save to localStorage immediately
    this.setItem(key, data);
    
    // If online and sync is enabled, try to sync to server
    if (this.isOnline && syncToServer) {
      try {
        await this.syncToAPI(key, data);
      } catch (error) {
        console.error('Error syncing to API:', error);
        // Add to sync queue for later
        this.addToSyncQueue({
          type: 'UPDATE',
          key,
          data,
          operation: 'setData'
        });
      }
    } else if (syncToServer) {
      // Add to sync queue if offline
      this.addToSyncQueue({
        type: 'UPDATE',
        key,
        data,
        operation: 'setData'
      });
    }
  }

  async deleteData(key: string, syncToServer = true): Promise<void> {
    // Remove from localStorage
    this.removeItem(key);
    
    // If online and sync is enabled, try to sync to server
    if (this.isOnline && syncToServer) {
      try {
        await this.deleteFromAPI(key);
      } catch (error) {
        console.error('Error deleting from API:', error);
        // Add to sync queue for later
        this.addToSyncQueue({
          type: 'DELETE',
          key,
          operation: 'deleteData'
        });
      }
    } else if (syncToServer) {
      // Add to sync queue if offline
      this.addToSyncQueue({
        type: 'DELETE',
        key,
        operation: 'deleteData'
      });
    }
  }

  // Mock API operations (replace with actual API calls)
  private async fetchFromAPI(key: string): Promise<any> {
    // This would be replaced with actual API endpoints
    console.log(`Fetching ${key} from API...`);
    return null;
  }

  private async syncToAPI(key: string, data: any): Promise<void> {
    // This would be replaced with actual API endpoints
    console.log(`Syncing ${key} to API...`, data);
  }

  private async deleteFromAPI(key: string): Promise<void> {
    // This would be replaced with actual API endpoints
    console.log(`Deleting ${key} from API...`);
  }

  // Sync pending operations
  async syncPendingOperations(): Promise<void> {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }

    console.log(`Syncing ${this.syncQueue.length} pending operations...`);
    
    const operations = [...this.syncQueue];
    this.syncQueue = [];
    this.saveSyncQueue();

    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'UPDATE':
            await this.syncToAPI(operation.key, operation.data);
            break;
          case 'DELETE':
            await this.deleteFromAPI(operation.key);
            break;
        }
        console.log(`Synced operation: ${operation.type} ${operation.key}`);
      } catch (error) {
        console.error(`Failed to sync operation: ${operation.type} ${operation.key}`, error);
        // Re-add to sync queue
        this.syncQueue.push(operation);
      }
    }
    
    this.saveSyncQueue();
  }

  // Utility methods
  isOffline(): boolean {
    return !this.isOnline;
  }

  getPendingSyncCount(): number {
    return this.syncQueue.length;
  }

  clearAllData(): void {
    if (typeof window === 'undefined') return;
    
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith(this.storagePrefix)
    );
    
    keys.forEach(key => localStorage.removeItem(key));
    this.syncQueue = [];
  }

  // Export/Import for backup
  exportData(): any {
    if (typeof window === 'undefined') return {};
    
    const data: any = {};
    const keys = Object.keys(localStorage).filter(key => 
      key.startsWith(this.storagePrefix)
    );
    
    keys.forEach(key => {
      const cleanKey = key.replace(this.storagePrefix, '');
      data[cleanKey] = this.getItem(cleanKey);
    });
    
    return data;
  }

  importData(data: any): void {
    Object.keys(data).forEach(key => {
      this.setItem(key, data[key]);
    });
  }
}

// Create singleton instance
const offlineDataService = new OfflineDataService();

export default offlineDataService;

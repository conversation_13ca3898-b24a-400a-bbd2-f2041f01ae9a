// Performance Optimization Utilities
// Implements data preloading, caching, reduced animations, and Turbo monitoring

import { useEffect, useRef, useState } from 'react';
import { performanceMonitor } from './performanceMonitor';

// Cache for API responses
const apiCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// Cache TTL in milliseconds (5 minutes default)
const DEFAULT_CACHE_TTL = 5 * 60 * 1000;

// Performance-optimized API service with caching
export class PerformanceApiService {
  private static instance: PerformanceApiService;
  private baseURL: string;
  private preloadQueue: Set<string> = new Set();

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
  }

  static getInstance(baseURL?: string): PerformanceApiService {
    if (!PerformanceApiService.instance) {
      PerformanceApiService.instance = new PerformanceApiService(baseURL);
    }
    return PerformanceApiService.instance;
  }

  // Get data with caching
  async get(endpoint: string, options: { cache?: boolean; ttl?: number } = {}): Promise<any> {
    const { cache = true, ttl = DEFAULT_CACHE_TTL } = options;
    const cacheKey = `GET:${endpoint}`;

    // Check cache first
    if (cache && apiCache.has(cacheKey)) {
      const cached = apiCache.get(cacheKey)!;
      if (Date.now() - cached.timestamp < cached.ttl) {
        return cached.data;
      }
      apiCache.delete(cacheKey);
    }

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Cache the response
      if (cache) {
        apiCache.set(cacheKey, {
          data,
          timestamp: Date.now(),
          ttl,
        });
      }

      return data;
    } catch (error) {
      console.error(`API Error for ${endpoint}:`, error);
      throw error;
    }
  }

  // Preload data for better performance
  async preload(endpoints: string[]): Promise<void> {
    const preloadPromises = endpoints
      .filter(endpoint => !this.preloadQueue.has(endpoint))
      .map(async (endpoint) => {
        this.preloadQueue.add(endpoint);
        try {
          await this.get(endpoint, { cache: true, ttl: DEFAULT_CACHE_TTL });
        } catch (error) {
          console.warn(`Preload failed for ${endpoint}:`, error);
        } finally {
          this.preloadQueue.delete(endpoint);
        }
      });

    await Promise.allSettled(preloadPromises);
  }

  // Clear cache
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of apiCache.keys()) {
        if (key.includes(pattern)) {
          apiCache.delete(key);
        }
      }
    } else {
      apiCache.clear();
    }
  }

  // Get cache stats
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: apiCache.size,
      keys: Array.from(apiCache.keys()),
    };
  }
}

// Hook for performance-optimized data fetching
export function usePerformantData<T>(
  endpoint: string,
  options: {
    preload?: string[];
    cache?: boolean;
    ttl?: number;
    dependencies?: any[];
  } = {}
) {
  const { preload = [], cache = true, ttl = DEFAULT_CACHE_TTL, dependencies = [] } = options;
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const apiService = PerformanceApiService.getInstance();

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Preload related data
        if (preload.length > 0) {
          apiService.preload(preload);
        }

        // Fetch main data
        const result = await apiService.get(endpoint, { cache, ttl });
        
        if (isMounted) {
          setData(result);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'An error occurred');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [endpoint, cache, ttl, ...dependencies]);

  return { data, loading, error, refetch: () => fetchData() };
}

// Hook for intersection observer (lazy loading)
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
): [React.RefObject<HTMLDivElement>, boolean] {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [options]);

  return [ref, isIntersecting];
}

// Debounce hook for search inputs
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Performance monitoring (enhanced with Turbo support)
export class PerformanceMonitor {
  private static metrics: Map<string, number[]> = new Map();

  static startTiming(label: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (!this.metrics.has(label)) {
        this.metrics.set(label, []);
      }

      this.metrics.get(label)!.push(duration);

      // Keep only last 100 measurements
      const measurements = this.metrics.get(label)!;
      if (measurements.length > 100) {
        measurements.shift();
      }

      // Log to Turbo performance monitor if enabled
      if (process.env.NEXT_PUBLIC_PERFORMANCE_MONITORING === 'true') {
        console.log(`⚡ [Turbo Timing] ${label}: ${duration.toFixed(2)}ms`);
      }
    };
  }

  static getMetrics(label: string): { avg: number; min: number; max: number; count: number } | null {
    const measurements = this.metrics.get(label);
    if (!measurements || measurements.length === 0) return null;

    const avg = measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
    const min = Math.min(...measurements);
    const max = Math.max(...measurements);

    return { avg, min, max, count: measurements.length };
  }

  static getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};

    for (const [label] of this.metrics) {
      const metrics = this.getMetrics(label);
      if (metrics) {
        result[label] = metrics;
      }
    }

    return result;
  }

  // Export metrics to Turbo performance monitor
  static exportToTurboMonitor(): void {
    if (typeof window !== 'undefined' && performanceMonitor) {
      const allMetrics = this.getAllMetrics();
      console.log('🚀 [Performance Export] Exporting metrics to Turbo monitor:', allMetrics);
      performanceMonitor.exportMetrics();
    }
  }
}

// Reduced animation preferences
export const reducedMotionPreferences = {
  // Check if user prefers reduced motion
  prefersReducedMotion: () => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  // Get animation duration based on user preference
  getAnimationDuration: (normalDuration: number) => {
    return reducedMotionPreferences.prefersReducedMotion() ? 0 : normalDuration;
  },

  // Get animation class based on user preference
  getAnimationClass: (normalClass: string, reducedClass: string = '') => {
    return reducedMotionPreferences.prefersReducedMotion() ? reducedClass : normalClass;
  },
};

// Image optimization utilities
export const imageOptimization = {
  // Generate responsive image URLs
  getResponsiveImageUrl: (baseUrl: string, width: number, quality: number = 80) => {
    return `${baseUrl}?w=${width}&q=${quality}`;
  },

  // Lazy load images
  createLazyImageLoader: () => {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          const src = img.dataset.src;
          if (src) {
            img.src = src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    return imageObserver;
  },
};

// Bundle size optimization
export const bundleOptimization = {
  // Dynamic import wrapper
  dynamicImport: async <T>(importFn: () => Promise<T>): Promise<T> => {
    try {
      return await importFn();
    } catch (error) {
      console.error('Dynamic import failed:', error);
      throw error;
    }
  },

  // Preload critical resources
  preloadCriticalResources: (resources: string[]) => {
    resources.forEach((resource) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource;
      link.as = resource.endsWith('.css') ? 'style' : 'script';
      document.head.appendChild(link);
    });
  },
};

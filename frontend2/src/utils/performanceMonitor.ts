/**
 * Performance monitoring utilities for Turbo-enabled Next.js app
 */

interface PerformanceMetrics {
  buildTime?: number;
  hotReloadTime?: number;
  bundleSize?: number;
  pageLoadTime?: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = process.env.NEXT_PUBLIC_PERFORMANCE_MONITORING === 'true';
  }

  // Measure page load performance
  measurePageLoad() {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // Use Performance Observer API for Web Vitals
    if ('PerformanceObserver' in window) {
      // First Contentful Paint
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
            this.logMetric('First Contentful Paint', entry.startTime);
          }
        }
      }).observe({ entryTypes: ['paint'] });

      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
        this.logMetric('Largest Contentful Paint', lastEntry.startTime);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
        this.logMetric('Cumulative Layout Shift', clsValue);
      }).observe({ entryTypes: ['layout-shift'] });
    }

    // Basic page load timing
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.metrics.pageLoadTime = loadTime;
      this.logMetric('Page Load Time', loadTime);
    });
  }

  // Measure hot reload performance (development only)
  measureHotReload() {
    if (!this.isEnabled || process.env.NODE_ENV !== 'development') return;

    const startTime = performance.now();
    
    // Listen for Next.js hot reload events
    if (typeof window !== 'undefined' && (window as any).__NEXT_DATA__) {
      const observer = new MutationObserver(() => {
        const endTime = performance.now();
        const hotReloadTime = endTime - startTime;
        this.metrics.hotReloadTime = hotReloadTime;
        this.logMetric('Hot Reload Time', hotReloadTime);
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
  }

  // Log performance metrics
  private logMetric(name: string, value: number) {
    if (!this.isEnabled) return;

    const formattedValue = name.includes('Time') ? `${value.toFixed(2)}ms` : value.toFixed(4);
    console.log(`🚀 [Turbo Performance] ${name}: ${formattedValue}`);
  }

  // Get all metrics
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Export metrics for analysis
  exportMetrics() {
    if (!this.isEnabled) return;

    const metricsData = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      turboEnabled: process.env.TURBOPACK_ENABLED === 'true',
      ...this.metrics
    };

    console.table(metricsData);
    return metricsData;
  }

  // Compare with baseline metrics
  compareWithBaseline(baseline: PerformanceMetrics) {
    if (!this.isEnabled) return;

    const comparison = Object.entries(this.metrics).map(([key, value]) => {
      const baselineValue = baseline[key as keyof PerformanceMetrics];
      if (baselineValue && value) {
        const improvement = ((baselineValue - value) / baselineValue) * 100;
        return {
          metric: key,
          current: value,
          baseline: baselineValue,
          improvement: `${improvement > 0 ? '+' : ''}${improvement.toFixed(2)}%`
        };
      }
      return null;
    }).filter(Boolean);

    console.table(comparison);
    return comparison;
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Auto-initialize in browser
if (typeof window !== 'undefined') {
  performanceMonitor.measurePageLoad();
  performanceMonitor.measureHotReload();
}

export default performanceMonitor;
